# LinkedIn People APIs Complete Guide

## 🎯 **KEY DIFFERENCES: People Profile vs People Search**

| Aspect | **People Profile Collect** | **People Search Collect** |
|--------|---------------------------|---------------------------|
| **Purpose** | Collect DETAILED profile data from specific LinkedIn URLs | SEARCH for people by name and collect basic info |
| **Input** | LinkedIn profile URLs | First name + Last name + base URL |
| **Data Depth** | COMPREHENSIVE (experience, education, posts, etc.) | BASIC (name, title, location, summary) |
| **Use Case** | When you know exact profile URLs | When you want to find people by name |
| **Base Path** | `/linkedin/people-profile/collect` | `/linkedin/people-search-collect` |

---

## 🔵 **PEOPLE PROFILE COLLECT API**

### **What it does:**
- Scrapes DETAILED LinkedIn profile information from specific URLs
- Gets comprehensive data: experience, education, posts, certifications, etc.
- Perfect when you already have LinkedIn profile URLs

### **Endpoints:**
- `POST /linkedin/people-profile/collect` - Start collection
- `GET /linkedin/people-profile/collect` - Get all profiles
- `GET /linkedin/people-profile/collect/{id}` - Get by database ID
- `GET /linkedin/people-profile/collect/linkedin-id/{linkedinId}` - Get by LinkedIn ID
- `GET /linkedin/people-profile/collect/snapshot/{snapshotId}/status` - Check status
- `GET /linkedin/people-profile/collect/snapshot/{snapshotId}/data` - Get data

### **Input JSON for POST:**
```json
{
  "urls": [
    "https://www.linkedin.com/in/elad-moshe-05a90413/",
    "https://www.linkedin.com/in/jonathan-myrvik-3baa01109/",
    "https://www.linkedin.com/in/aviv-tal-75b81/"
  ]
}
```

### **Response (Async Operation):**
```json
{
  "success": true,
  "message": "Data collection started successfully. Use the snapshot_id to check status and retrieve data when ready.",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "instructions": {
    "check_status": "GET /linkedin/people-profile/collect/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/people-profile/collect/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

### **Final Data Structure (Rich Profile Data):**
```json
{
  "id": "uuid-here",
  "linkedin_num_id": "*********",
  "url": "https://www.linkedin.com/in/praneeth-devarasetty/",
  "name": "Praneeth Devarasetty",
  "first_name": "Praneeth",
  "last_name": "Devarasetty",
  "country_code": "US",
  "city": "San Francisco, CA",
  "about": "Software Engineer passionate about building scalable systems...",
  "followers": 1200,
  "connections": 500,
  "position": "Senior Software Engineer at Google",
  "experience": [
    {
      "title": "Senior Software Engineer",
      "company": "Google",
      "duration": "2022 - Present",
      "location": "Mountain View, CA",
      "description": "Building scalable distributed systems..."
    }
  ],
  "current_company": {
    "name": "Google",
    "company_id": "1441",
    "url": "https://www.linkedin.com/company/google/"
  },
  "education": [
    {
      "school": "Stanford University",
      "degree": "Master of Science",
      "field": "Computer Science",
      "years": "2020 - 2022"
    }
  ],
  "certifications": [
    {
      "name": "AWS Solutions Architect",
      "issuer": "Amazon Web Services",
      "date": "2023"
    }
  ],
  "posts": [
    {
      "text": "Excited to share our latest project...",
      "date": "2024-01-15",
      "reactions": 45,
      "comments": 12
    }
  ],
  "languages": [
    {
      "name": "English",
      "proficiency": "Native"
    }
  ],
  "avatar": "https://media.licdn.com/dms/image/...",
  "timestamp": "2025-07-20"
}
```

---

## 🟢 **PEOPLE SEARCH COLLECT API**

### **What it does:**
- SEARCHES for people by first name + last name
- Returns BASIC profile information from search results
- Perfect for finding people when you only know their names

### **Endpoints:**
- `POST /linkedin/people-search-collect` - Start search collection
- `GET /linkedin/people-search-collect` - Get all collected people (paginated)
- `GET /linkedin/people-search-collect/search` - Search by criteria
- `GET /linkedin/people-search-collect/location/{location}` - Get by location
- `GET /linkedin/people-search-collect/{id}` - Get by database ID
- `PATCH /linkedin/people-search-collect/{id}` - Update person
- `DELETE /linkedin/people-search-collect/{id}` - Delete person
- `GET /linkedin/people-search-collect/snapshot/{snapshotId}/status` - Check status
- `GET /linkedin/people-search-collect/snapshot/{snapshotId}/data` - Get data

### **Input JSON for POST:**
```json
{
  "searches": [
    {
      "url": "https://www.linkedin.com",
      "first_name": "james",
      "last_name": "smith"
    },
    {
      "url": "https://www.linkedin.com",
      "first_name": "Lisa",
      "last_name": "Ledger"
    }
  ]
}
```

### **Response (Async Operation):**
```json
{
  "success": true,
  "message": "LinkedIn people search collection started successfully. Use the snapshot_id to check status and retrieve data when ready.",
  "snapshot_id": "s_mdea2wuk1dr7b21l7r",
  "status": "started",
  "searches_count": 2,
  "instructions": {
    "check_status": "GET /linkedin/people-search-collect/snapshot/s_mdea2wuk1dr7b21l7r/status",
    "get_data": "GET /linkedin/people-search-collect/snapshot/s_mdea2wuk1dr7b21l7r/data"
  }
}
```

### **Final Data Structure (Basic Search Results):**
```json
{
  "id": "uuid-here",
  "name": "James Smith",
  "subtitle": "Senior Software Engineer at Google",
  "location": "United Kingdom",
  "experience": "Corporate Sales",
  "education": "Stanford University",
  "avatar": "https://media.licdn.com/dms/image/...",
  "url": "https://www.linkedin.com/in/james-smith-123/",
  "search_first_name": "james",
  "search_last_name": "smith",
  "search_url": "https://www.linkedin.com",
  "input_url": "https://www.linkedin.com",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

---

## 🔄 **ASYNC WORKFLOW FOR BOTH APIS**

Both APIs use the same async pattern:

### **Step 1: Start Collection**
```javascript
// POST request returns snapshot_id
const response = await fetch('/linkedin/people-profile/collect', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    urls: ["https://www.linkedin.com/in/someone/"]
  })
});

const { snapshot_id } = await response.json();
```

### **Step 2: Check Status**
```javascript
// Keep checking until status is "completed"
const checkStatus = async () => {
  const response = await fetch(`/linkedin/people-profile/collect/snapshot/${snapshot_id}/status`);
  const { status } = await response.json();
  
  if (status === 'completed') {
    // Ready to get data
    return true;
  } else if (status === 'running') {
    // Still processing, check again later
    setTimeout(checkStatus, 5000); // Check every 5 seconds
  }
};
```

### **Step 3: Get Data**
```javascript
// Once status is "completed", get the actual data
const getData = async () => {
  const response = await fetch(`/linkedin/people-profile/collect/snapshot/${snapshot_id}/data`, {
    headers: { 'Authorization': 'Bearer YOUR_TOKEN' }
  });
  
  const result = await response.json();
  console.log('Collected data:', result.data);
};
```

---

## 📊 **COMPLETE USAGE EXAMPLES**

### **Example 1: Collect Detailed Profile Data**
```javascript
// When you have specific LinkedIn URLs and want detailed data
const collectProfiles = async () => {
  try {
    // Step 1: Start collection
    const response = await fetch('/linkedin/people-profile/collect', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_TOKEN'
      },
      body: JSON.stringify({
        urls: [
          "https://www.linkedin.com/in/elad-moshe-05a90413/",
          "https://www.linkedin.com/in/jonathan-myrvik-3baa01109/"
        ]
      })
    });
    
    const { snapshot_id } = await response.json();
    
    // Step 2: Monitor status
    const waitForCompletion = async () => {
      const statusResponse = await fetch(`/linkedin/people-profile/collect/snapshot/${snapshot_id}/status`);
      const { status } = await statusResponse.json();
      
      if (status === 'completed') {
        // Step 3: Get data
        const dataResponse = await fetch(`/linkedin/people-profile/collect/snapshot/${snapshot_id}/data`, {
          headers: { 'Authorization': 'Bearer YOUR_TOKEN' }
        });
        const result = await dataResponse.json();
        console.log('Detailed profiles:', result.data);
      } else {
        setTimeout(waitForCompletion, 5000);
      }
    };
    
    waitForCompletion();
  } catch (error) {
    console.error('Error:', error);
  }
};
```

### **Example 2: Search for People by Name**
```javascript
// When you want to find people by their names
const searchPeople = async () => {
  try {
    // Step 1: Start search
    const response = await fetch('/linkedin/people-search-collect', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_TOKEN'
      },
      body: JSON.stringify({
        searches: [
          {
            url: "https://www.linkedin.com",
            first_name: "james",
            last_name: "smith"
          },
          {
            url: "https://www.linkedin.com",
            first_name: "sarah",
            last_name: "johnson"
          }
        ]
      })
    });
    
    const { snapshot_id } = await response.json();
    
    // Step 2: Monitor and get data (same pattern as above)
    // ... similar async workflow
  } catch (error) {
    console.error('Error:', error);
  }
};
```

### **Example 3: Get All Collected People (Paginated)**
```javascript
// Get all people you've collected with pagination
const getAllPeople = async () => {
  try {
    const response = await fetch('/linkedin/people-search-collect?page=1&limit=20', {
      headers: { 'Authorization': 'Bearer YOUR_TOKEN' }
    });
    
    const result = await response.json();
    console.log('People:', result.data);
    console.log('Total:', result.total);
    console.log('Pages:', result.totalPages);
  } catch (error) {
    console.error('Error:', error);
  }
};
```

### **Example 4: Search Collected People by Criteria**
```javascript
// Search through your collected people
const searchCollectedPeople = async () => {
  try {
    const response = await fetch('/linkedin/people-search-collect/search?first_name=james&location=United Kingdom');
    const people = await response.json();
    console.log('Matching people:', people);
  } catch (error) {
    console.error('Error:', error);
  }
};
```

---

## 🎯 **WHEN TO USE WHICH API**

### **Use People Profile Collect When:**
- ✅ You have specific LinkedIn profile URLs
- ✅ You need DETAILED information (experience, education, posts, etc.)
- ✅ You want comprehensive profile data
- ✅ You're doing deep analysis of specific individuals

### **Use People Search Collect When:**
- ✅ You only know first name + last name
- ✅ You want to FIND people, not analyze them deeply
- ✅ You need basic contact/summary information
- ✅ You're building a lead generation system
- ✅ You want to search and filter results

---

## ⚠️ **IMPORTANT NOTES**

1. **Both APIs are ASYNC** - They return snapshot_id, not immediate data
2. **Authentication Required** - All endpoints need JWT token
3. **Rate Limits** - Both APIs have scraping limits
4. **Data Persistence** - Both save data to your database
5. **Different Data Depth** - Profile = detailed, Search = basic
6. **URL Requirements** - Profile needs exact URLs, Search needs names

---

## 🔍 **ERROR HANDLING**

### **Common Errors:**
```javascript
// 400 - Bad Request
{
  "statusCode": 400,
  "message": ["urls must contain at least 1 elements"],
  "error": "Bad Request"
}

// 401 - Unauthorized
{
  "statusCode": 401,
  "message": "Unauthorized",
  "error": "Unauthorized"
}

// 404 - Not Found
{
  "statusCode": 404,
  "message": "Person not found",
  "error": "Not Found"
}

// 500 - Server Error
{
  "statusCode": 500,
  "message": "LinkedIn People Search Collect dataset ID is not configured",
  "error": "Internal Server Error"
}
```

This guide provides everything you need to understand and use both People APIs effectively!