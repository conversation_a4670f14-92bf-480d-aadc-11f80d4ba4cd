{"openapi": "3.0.0", "info": {"title": "LinkedIn Company Information Collection API", "description": "API for collecting and managing LinkedIn company information", "version": "1.0.0"}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}], "security": [{"bearerAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"CompanyUrlDto": {"type": "object", "required": ["urls"], "properties": {"urls": {"type": "array", "items": {"type": "string", "format": "uri", "pattern": "linkedin\\.com\\/company", "example": "https://www.linkedin.com/company/mighty-networks/"}, "minItems": 1, "maxItems": 50, "description": "Array of LinkedIn company URLs to collect information from"}}}, "CompanyInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Database record ID"}, "company_id": {"type": "string", "description": "LinkedIn company identifier", "example": "2330778"}, "name": {"type": "string", "description": "Company name", "example": "Mighty Networks"}, "website": {"type": "string", "format": "uri", "description": "Company website URL", "example": "https://www.mightynetworks.com/"}, "phone": {"type": "string", "description": "Company phone number", "example": "******-123-4567"}, "description": {"type": "string", "description": "Short company description"}, "about": {"type": "string", "description": "Detailed company about section", "example": "Communities made for people magic. Nearly $500M in creator earnings on Mighty."}, "url": {"type": "string", "format": "uri", "description": "LinkedIn company page URL"}, "image_url": {"type": "string", "format": "uri", "description": "Company logo image URL"}, "background_image_url": {"type": "string", "format": "uri", "description": "Company background image URL"}, "followers": {"type": "integer", "description": "Number of LinkedIn followers", "example": 39634}, "organization_type": {"type": "string", "description": "Type of organization", "example": "Private Company"}, "employees": {"type": "integer", "description": "Number of employees", "example": 150}, "employees_range": {"type": "string", "description": "Employee count range", "example": "51-200"}, "headquarters": {"type": "string", "description": "Company headquarters location", "example": "Palo Alto, California"}, "founded": {"type": "string", "description": "Year company was founded", "example": "2017"}, "industries": {"type": "array", "items": {"type": "string"}, "description": "Company industries", "example": ["Technology", "Information and Internet"]}, "headquarters_geolocation": {"type": "string", "description": "Geographic coordinates of headquarters"}, "specialities": {"type": "string", "description": "Company specialities"}, "locations": {"type": "array", "items": {"type": "object", "properties": {"description": {"type": "string"}, "is_hq": {"type": "boolean"}, "city": {"type": "string"}, "state": {"type": "string"}, "postal_code": {"type": "string"}, "country": {"type": "string"}, "address": {"type": "string"}, "address_2": {"type": "string"}, "street": {"type": "string"}, "is_primary": {"type": "boolean"}, "geographic_area": {"type": "string"}, "lat": {"type": "string"}, "lng": {"type": "string"}}}}, "social_media": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string", "format": "uri"}}}}, "employees_insights": {"type": "array", "items": {"type": "object", "properties": {"insight_type": {"type": "string"}, "value": {"type": "number"}, "label": {"type": "string"}}}}, "funding": {"type": "array", "items": {"type": "object", "properties": {"money_raised": {"type": "number"}, "money_raised_currency": {"type": "string"}, "announced_date": {"type": "string"}, "funding_stage": {"type": "string"}, "money_raised_formatted": {"type": "string"}, "lead_investors": {"type": "array", "items": {"type": "string"}}, "total_investors": {"type": "number"}, "investor_types": {"type": "array", "items": {"type": "string"}}}}}, "acquisitions": {"type": "array", "items": {"type": "object", "properties": {"company_name": {"type": "string"}, "announced_date": {"type": "string"}, "price": {"type": "string"}, "acquisition_type": {"type": "string"}, "industries": {"type": "array", "items": {"type": "string"}}, "company_url": {"type": "string", "format": "uri"}}}}, "similar_companies": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "image_url": {"type": "string", "format": "uri"}, "followers": {"type": "number"}, "industry": {"type": "string"}, "company_id": {"type": "string"}}}}, "affiliated_pages": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "image_url": {"type": "string", "format": "uri"}, "followers": {"type": "number"}, "industry": {"type": "string"}, "company_id": {"type": "string"}}}}, "showcase_pages": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "image_url": {"type": "string", "format": "uri"}, "followers": {"type": "number"}, "industry": {"type": "string"}, "company_id": {"type": "string"}}}}, "featured_groups": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "image_url": {"type": "string", "format": "uri"}, "members": {"type": "number"}, "group_id": {"type": "string"}}}}, "updates": {"type": "array", "items": {"type": "object", "properties": {"update_id": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "text": {"type": "string"}, "posted_date": {"type": "string"}, "image_url": {"type": "string", "format": "uri"}, "total_reactions": {"type": "number"}, "reactions": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "count": {"type": "number"}}}}}}}, "original_request_url": {"type": "string", "format": "uri", "description": "Original LinkedIn URL used for collection"}, "data_source": {"type": "string", "description": "Source of the data collection"}, "collection_status": {"type": "string", "description": "Status of data collection"}, "collection_error": {"type": "string", "description": "Error message if collection failed"}, "collected_at": {"type": "string", "format": "date-time", "description": "Timestamp when data was collected"}, "createdAt": {"type": "string", "format": "date-time", "description": "Record creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Record last update timestamp"}}}, "CollectResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Successfully collected 1 companies"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyInfo"}}, "saved_count": {"type": "number", "example": 1}, "snapshot_id": {"type": "string", "example": "s_mdbr9ydd1l1ey817fr"}}}, "ErrorResponse": {"type": "object", "properties": {"message": {"type": "string", "description": "Error message"}, "error": {"type": "string", "description": "Error type"}, "statusCode": {"type": "integer", "description": "HTTP status code"}}}}}, "paths": {"/linkedin/company-info/collect": {"post": {"tags": ["LinkedIn Company Information Collection"], "summary": "Collect LinkedIn company information by URLs", "description": "Scrape comprehensive LinkedIn company information using provided company URLs via BrightData API. Returns detailed company data immediately after collection completes.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyUrlDto"}, "examples": {"single_company": {"summary": "Single company URL", "value": {"urls": ["https://www.linkedin.com/company/mighty-networks/"]}}, "multiple_companies": {"summary": "Multiple company URLs", "value": {"urls": ["https://www.linkedin.com/company/mighty-networks/", "https://www.linkedin.com/company/microsoft/", "https://www.linkedin.com/company/google/"]}}}}}}, "responses": {"200": {"description": "Successfully collected company information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectResponse"}}}}, "400": {"description": "Invalid request body or URL format", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"invalid_url": {"summary": "Invalid URL format", "value": {"message": "Must be a LinkedIn company URL", "error": "Bad Request", "statusCode": 400}}, "missing_urls": {"summary": "Missing URLs array", "value": {"message": "At least one URL is required", "error": "Bad Request", "statusCode": 400}}}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error during data collection", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "get": {"tags": ["LinkedIn Company Information Collection"], "summary": "Get all collected company information", "description": "Retrieve all company information records from database for the authenticated user", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of records per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "Successfully retrieved company information records", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyInfo"}}, "total": {"type": "integer", "description": "Total number of records"}, "page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Records per page"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/linkedin/company-info/collect/company/{companyId}": {"get": {"tags": ["LinkedIn Company Information Collection"], "summary": "Get company information by company ID", "description": "Retrieve company information by LinkedIn company ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "companyId", "in": "path", "required": true, "description": "LinkedIn company identifier", "schema": {"type": "string", "example": "2330778"}}], "responses": {"200": {"description": "Successfully retrieved company information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyInfo"}}}}, "404": {"description": "Company not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/linkedin/company-info/collect/url/{encodedUrl}": {"get": {"tags": ["LinkedIn Company Information Collection"], "summary": "Get company information by original URL", "description": "Retrieve company information by original LinkedIn URL (URL encoded)", "security": [{"bearerAuth": []}], "parameters": [{"name": "encodedUrl", "in": "path", "required": true, "description": "URL encoded LinkedIn company URL", "schema": {"type": "string", "example": "https%3A%2F%2Fwww.linkedin.com%2Fcompany%2Fmighty-networks%2F"}}], "responses": {"200": {"description": "Successfully retrieved company information records", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyInfo"}}}}}, "404": {"description": "No company information found for the given URL", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/linkedin/company-info/collect/{id}": {"get": {"tags": ["LinkedIn Company Information Collection"], "summary": "Get company information by record ID", "description": "Retrieve specific company information by database record ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Database record ID (UUID)", "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Successfully retrieved company information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyInfo"}}}}, "404": {"description": "Company information record not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["LinkedIn Company Information Collection"], "summary": "Delete company information record", "description": "Delete a specific company information record by ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Database record ID to delete", "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Successfully deleted company information record", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Company information deleted successfully"}}}}}}, "404": {"description": "Company information record not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Company information not found"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}}