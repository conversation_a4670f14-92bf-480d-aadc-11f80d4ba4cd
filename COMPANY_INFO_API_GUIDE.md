# LinkedIn Company Information Collection API Guide

## 🚨 SOLVING THE 404 ERROR

### The Problem
You're getting this error:
```json
{
  "message": "Cannot GET /linkedin/company-info/search?name=people",
  "error": "Not Found", 
  "statusCode": 404
}
```

### The Solution
**The endpoint `/linkedin/company-info/search` does NOT exist.** You're trying to access a non-existent endpoint.

### Correct Endpoints Available:
1. `POST /linkedin/company-info/collect` - Collect company data
2. `GET /linkedin/company-info/collect` - Get all collected data
3. `GET /linkedin/company-info/collect/company/{companyId}` - Get by company ID
4. `GET /linkedin/company-info/collect/url/{encodedUrl}` - Get by URL
5. `GET /linkedin/company-info/collect/{id}` - Get by record ID
6. `DELETE /linkedin/company-info/collect/{id}` - Delete record

---

## 📋 WHEN TO USE POST vs GET

### 🔵 Use POST `/linkedin/company-info/collect`
**Purpose:** To scrape NEW company information from LinkedIn

**When to use:**
- You want to collect fresh data from LinkedIn
- You have LinkedIn company URLs to scrape
- You need to trigger data collection process

**Input JSON Example:**
```json
{
  "urls": [
    "https://www.linkedin.com/company/mighty-networks/",
    "https://www.linkedin.com/company/microsoft/",
    "https://www.linkedin.com/company/google/"
  ]
}
```

**Response Example:**
```json
{
  "success": true,
  "message": "Successfully collected 3 companies",
  "data": [
    {
      "id": "uuid-here",
      "company_id": "2330778",
      "name": "Mighty Networks",
      "website": "https://www.mightynetworks.com/",
      "about": "Communities made for people magic...",
      "followers": 39634,
      "headquarters": "Palo Alto, California",
      "founded": "2017",
      "industries": ["Technology", "Information and Internet"]
    }
  ],
  "saved_count": 3,
  "snapshot_id": "s_mdbr9ydd1l1ey817fr"
}
```

### 🟢 Use GET `/linkedin/company-info/collect`
**Purpose:** To retrieve EXISTING company information from your database

**When to use:**
- You want to see all companies you've already collected
- You need to display a list of saved companies
- You want to paginate through your collected data

**No input needed** (it's a GET request)

**Query Parameters (optional):**
- `?page=1` - Page number
- `?limit=10` - Records per page

**Response Example:**
```json
{
  "data": [
    {
      "id": "uuid-1",
      "company_id": "2330778",
      "name": "Mighty Networks",
      "createdAt": "2024-01-15T10:30:00Z"
    },
    {
      "id": "uuid-2", 
      "company_id": "1441",
      "name": "Microsoft",
      "createdAt": "2024-01-14T15:20:00Z"
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 10
}
```

---

## 🔧 COMPLETE API USAGE EXAMPLES

### 1. Collect New Company Data (POST)
```javascript
// Frontend JavaScript Example
const collectCompanyData = async () => {
  try {
    const response = await fetch('/linkedin/company-info/collect', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_JWT_TOKEN'
      },
      body: JSON.stringify({
        urls: [
          "https://www.linkedin.com/company/mighty-networks/",
          "https://www.linkedin.com/company/microsoft/"
        ]
      })
    });
    
    const result = await response.json();
    console.log('Collected data:', result);
  } catch (error) {
    console.error('Error collecting data:', error);
  }
};
```

### 2. Get All Collected Companies (GET)
```javascript
// Frontend JavaScript Example
const getAllCompanies = async () => {
  try {
    const response = await fetch('/linkedin/company-info/collect?page=1&limit=20', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN'
      }
    });
    
    const result = await response.json();
    console.log('All companies:', result);
  } catch (error) {
    console.error('Error fetching companies:', error);
  }
};
```

### 3. Get Company by ID (GET)
```javascript
// Frontend JavaScript Example
const getCompanyById = async (recordId) => {
  try {
    const response = await fetch(`/linkedin/company-info/collect/${recordId}`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN'
      }
    });
    
    const result = await response.json();
    console.log('Company details:', result);
  } catch (error) {
    console.error('Error fetching company:', error);
  }
};
```

### 4. Get Company by LinkedIn Company ID (GET)
```javascript
// Frontend JavaScript Example
const getCompanyByLinkedInId = async (companyId) => {
  try {
    const response = await fetch(`/linkedin/company-info/collect/company/${companyId}`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN'
      }
    });
    
    const result = await response.json();
    console.log('Company by LinkedIn ID:', result);
  } catch (error) {
    console.error('Error fetching company:', error);
  }
};
```

### 5. Get Company by Original URL (GET)
```javascript
// Frontend JavaScript Example
const getCompanyByUrl = async (linkedinUrl) => {
  try {
    const encodedUrl = encodeURIComponent(linkedinUrl);
    const response = await fetch(`/linkedin/company-info/collect/url/${encodedUrl}`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN'
      }
    });
    
    const result = await response.json();
    console.log('Company by URL:', result);
  } catch (error) {
    console.error('Error fetching company:', error);
  }
};
```

### 6. Delete Company Record (DELETE)
```javascript
// Frontend JavaScript Example
const deleteCompany = async (recordId) => {
  try {
    const response = await fetch(`/linkedin/company-info/collect/${recordId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN'
      }
    });
    
    const result = await response.json();
    console.log('Delete result:', result);
  } catch (error) {
    console.error('Error deleting company:', error);
  }
};
```

---

## 🎯 QUICK DECISION GUIDE

| What do you want to do? | Use this endpoint | Method |
|-------------------------|-------------------|---------|
| Scrape new company data from LinkedIn | `/linkedin/company-info/collect` | POST |
| See all companies I've collected | `/linkedin/company-info/collect` | GET |
| Find a specific company by database ID | `/linkedin/company-info/collect/{id}` | GET |
| Find a company by LinkedIn company ID | `/linkedin/company-info/collect/company/{companyId}` | GET |
| Find a company by its LinkedIn URL | `/linkedin/company-info/collect/url/{encodedUrl}` | GET |
| Remove a company from my database | `/linkedin/company-info/collect/{id}` | DELETE |

---

## ⚠️ IMPORTANT NOTES

1. **Authentication Required:** All endpoints require a valid JWT token in the Authorization header
2. **URL Encoding:** When using the URL endpoint, make sure to URL-encode the LinkedIn URL
3. **Rate Limits:** POST requests trigger data scraping and may have rate limits
4. **Data Persistence:** POST saves data to your database, GET retrieves existing data
5. **Error Handling:** Always handle 404, 401, and 500 errors in your frontend

---

## 🔍 TROUBLESHOOTING

### Common Issues:

1. **404 Error:** You're using a wrong endpoint URL
   - ❌ `/linkedin/company-info/search` (doesn't exist)
   - ✅ `/linkedin/company-info/collect` (correct)

2. **401 Error:** Missing or invalid authentication
   - Add `Authorization: Bearer YOUR_TOKEN` header

3. **400 Error:** Invalid request body
   - Check your JSON format and URL validation

4. **500 Error:** Server error during scraping
   - Check if LinkedIn URLs are valid and accessible

### Need to Search Companies?
If you need to search for companies by name (like your error suggests), you might need:
- A different endpoint for company search
- Or use the existing endpoints to filter collected data on the frontend