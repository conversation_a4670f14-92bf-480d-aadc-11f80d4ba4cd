# LinkedIn Backend API Integration Guide

## Overview

This comprehensive guide provides all the necessary information for frontend AI to integrate with the LinkedIn Backend API. The backend is built with NestJS, TypeScript, and uses Supabase for authentication with user-based data isolation.

## Base Configuration

### Server Details
- **Base URL**: `http://localhost:3000`
- **Swagger Documentation**: `http://localhost:3000/api-docs`
- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with TypeORM
- **Authentication**: Supabase JWT Bearer Token
- **Validation**: Zod schemas with comprehensive error handling

### Authentication
All API endpoints require Bearer token authentication using Supabase JWT tokens.

**Headers Required:**
```
Authorization: Bearer <supabase_jwt_token>
Content-Type: application/json
```

**User Isolation:**
- All data is isolated by user_id extracted from the JWT token
- Users can only access their own data
- No manual user_id parameter needed in requests

## API Endpoints Overview

### 1. LinkedIn Posts - Discover by URL
**Endpoint**: `POST /linkedin/post-discover-url`
**Purpose**: Discover LinkedIn posts from profile/company URLs with BrightData integration

### 2. LinkedIn People Profile - Collect
**Endpoint**: `POST /linkedin/people-profile/collect`
**Purpose**: Collect detailed LinkedIn profiles by specific profile URLs

### 3. LinkedIn People Profile - Discover by Name
**Endpoint**: `POST /linkedin/people-profile/discover`
**Purpose**: Discover LinkedIn profiles by searching with first and last names

### 4. LinkedIn Company Info - Collect
**Endpoint**: `POST /linkedin/company-info/collect`
**Purpose**: Collect comprehensive company information from LinkedIn company URLs

### 5. LinkedIn Job Listing - Collect
**Endpoint**: `POST /linkedin/job-listing/collect`
**Purpose**: Collect job listings from specific LinkedIn job URLs

### 6. LinkedIn Job Listing - Discover by Keyword
**Endpoint**: `POST /linkedin/job-listing/discover-keyword`
**Purpose**: Discover job listings using search parameters (location, keywords, filters)

### 7. LinkedIn Job Listing - Discover by URL
**Endpoint**: `POST /linkedin/job-listing/discover-url`
**Purpose**: Discover job listings from LinkedIn job search/company pages

### 8. LinkedIn Post - Collect
**Endpoint**: `POST /linkedin/post-collect`
**Purpose**: Collect posts and articles from specific LinkedIn post/pulse URLs

### 9. LinkedIn Post - Discover by Company
**Endpoint**: `POST /linkedin/post-discover-company`
**Purpose**: Discover posts from LinkedIn company pages

### 10. LinkedIn Post - Discover by Profile
**Endpoint**: `POST /linkedin/post-discover-profile`
**Purpose**: Discover posts from LinkedIn profile pages

### 11. LinkedIn People Search - Collect
**Endpoint**: `POST /linkedin/people-search-collect`
**Purpose**: Search and collect people profiles using search criteria

## Detailed API Specifications

### 1. LinkedIn Posts - Discover by URL

**POST** `/linkedin/post-discover-url`

**Request Body:**
```json
{
  "urls": [
    {
      "url": "https://linkedin.com/in/username",
      "limit": 50
    }
  ]
}
```

**Validation Rules:**
- `urls`: Array of 1-10 URL objects
- `url`: Valid URL string
- `limit`: Optional integer 1-100

**Success Response (200):**
```json
{
  "success": true,
  "message": "Post discovery started successfully",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "urls_count": 1,
  "instructions": {
    "check_status": "GET /linkedin/post-discover-url/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/post-discover-url/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

**Error Response (400):**
```json
{
  "message": "Validation failed",
  "error": "Bad Request",
  "statusCode": 400,
  "issues": [
    {
      "code": "invalid_string",
      "message": "Must be a valid URL",
      "path": ["urls", 0, "url"]
    }
  ]
}
```

**Additional Endpoints:**
- `GET /linkedin/post-discover-url` - Get all discovered posts
- `GET /linkedin/post-discover-url/snapshot/{snapshot_id}/status` - Check operation status
- `GET /linkedin/post-discover-url/snapshot/{snapshot_id}/data` - Get discovered data
- `GET /linkedin/post-discover-url/{id}` - Get specific post by ID

### 2. LinkedIn People Profile - Collect

**POST** `/linkedin/people-profile/collect`

**Request Body:**
```json
{
  "urls": [
    "https://linkedin.com/in/username1",
    "https://linkedin.com/in/username2"
  ]
}
```

**Validation Rules:**
- `urls`: Array of 1-100 LinkedIn profile URLs
- Each URL must match pattern: `/linkedin\.com\/in\//`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Data collection started successfully",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "instructions": {
    "check_status": "GET /linkedin/people-profile/collect/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/people-profile/collect/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

**Additional Endpoints:**
- `GET /linkedin/people-profile/collect` - Get all collected profiles
- `GET /linkedin/people-profile/collect/{id}` - Get specific profile by ID
- `GET /linkedin/people-profile/collect/snapshot/{snapshot_id}/status` - Check status
- `GET /linkedin/people-profile/collect/snapshot/{snapshot_id}/data` - Get data

### 3. LinkedIn People Profile - Discover by Name

**POST** `/linkedin/people-profile/discover`

**Request Body:**
```json
{
  "names": [
    {
      "first_name": "John",
      "last_name": "Doe"
    },
    {
      "first_name": "Jane",
      "last_name": "Smith"
    }
  ]
}
```

**Validation Rules:**
- `names`: Array of 1-50 name objects
- `first_name`: Required string 1-50 characters
- `last_name`: Required string 1-50 characters

**Success Response (200):**
```json
{
  "success": true,
  "message": "Profile discovery started successfully",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "names_count": 2,
  "instructions": {
    "check_status": "GET /linkedin/people-profile/discover/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/people-profile/discover/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

**Additional Endpoints:**
- `GET /linkedin/people-profile/discover` - Get all discovered profiles
- `GET /linkedin/people-profile/discover/{id}` - Get specific profile by ID

### 4. LinkedIn Company Info - Collect

**POST** `/linkedin/company-info/collect`

**Request Body:**
```json
{
  "urls": [
    "https://linkedin.com/company/company-name1",
    "https://linkedin.com/company/company-name2"
  ]
}
```

**Validation Rules:**
- `urls`: Array of 1-50 LinkedIn company URLs
- Each URL must match pattern: `/linkedin\.com\/company/`
- URL max length: 500 characters

**Success Response (200):**
```json
{
  "success": true,
  "message": "Company data collection started successfully",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "urls_count": 2,
  "instructions": {
    "check_status": "GET /linkedin/company-info/collect/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/company-info/collect/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

**Additional Endpoints:**
- `GET /linkedin/company-info/collect` - Get all collected companies
- `GET /linkedin/company-info/collect/{id}` - Get specific company by ID

### 5. LinkedIn Job Listing - Collect

**POST** `/linkedin/job-listing/collect`

**Request Body:**
```json
{
  "urls": [
    "https://linkedin.com/jobs/view/*********",
    "https://linkedin.com/jobs/view/*********"
  ]
}
```

**Validation Rules:**
- `urls`: Array of 1-100 LinkedIn job URLs
- Each URL must match pattern: `/linkedin\.com\/jobs\/view/`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Job listing collection started successfully",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "urls_count": 2,
  "instructions": {
    "check_status": "GET /linkedin/job-listing/collect/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/job-listing/collect/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

**Additional Endpoints:**
- `GET /linkedin/job-listing/collect` - Get all collected jobs
- `GET /linkedin/job-listing/collect/{id}` - Get specific job by ID

### 6. LinkedIn Job Listing - Discover by Keyword

**POST** `/linkedin/job-listing/discover-keyword`

**Request Body:**
```json
{
  "searches": [
    {
      "location": "New York, NY",
      "keyword": "Software Engineer",
      "country": "US",
      "time_range": "Past week",
      "job_type": "Full-time",
      "experience_level": "Mid-Senior level",
      "remote": "Remote",
      "company": "Google",
      "location_radius": "25 miles"
    }
  ]
}
```

**Validation Rules:**
- `searches`: Array of 1-50 search objects
- `location`: Required string 1-100 characters
- `keyword`: Required string 1-200 characters
- `country`: Optional string max 10 characters
- `time_range`: Optional enum ["Past 24 hours", "Past week", "Past month", "Any time"]
- `job_type`: Optional enum ["Full-time", "Part-time", "Contract", "Temporary", "Volunteer", "Internship", "Other"]
- `experience_level`: Optional enum ["Internship", "Entry level", "Associate", "Mid-Senior level", "Director", "Executive"]
- `remote`: Optional enum ["On-site", "Remote", "Hybrid"]
- `company`: Optional string max 100 characters
- `location_radius`: Optional string max 50 characters

**Success Response (200):**
```json
{
  "success": true,
  "message": "Job discovery started successfully",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "searches_count": 1,
  "instructions": {
    "check_status": "GET /linkedin/job-listing/discover-keyword/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/job-listing/discover-keyword/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

**Additional Endpoints:**
- `GET /linkedin/job-listing/discover-keyword` - Get all discovered jobs
- `GET /linkedin/job-listing/discover-keyword/{id}` - Get specific job by ID
- `GET /linkedin/job-listing/discover-keyword/location/{location}` - Filter by location

### 7. LinkedIn Job Listing - Discover by URL

**POST** `/linkedin/job-listing/discover-url`

**Request Body:**
```json
{
  "urls": [
    "https://linkedin.com/jobs/search/?keywords=engineer",
    "https://linkedin.com/company/google/jobs/",
    "https://linkedin.com/jobs/collections/recommended/"
  ]
}
```

**Validation Rules:**
- `urls`: Array of 1-50 LinkedIn job URLs
- Each URL must match pattern: `/linkedin\.com\/jobs/`
- URL max length: 500 characters

**Success Response (200):**
```json
{
  "success": true,
  "message": "Job discovery by URL started successfully",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "urls_count": 3,
  "instructions": {
    "check_status": "GET /linkedin/job-listing/discover-url/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/job-listing/discover-url/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

**Additional Endpoints:**
- `GET /linkedin/job-listing/discover-url` - Get all discovered jobs
- `GET /linkedin/job-listing/discover-url/{id}` - Get specific job by ID
- `GET /linkedin/job-listing/discover-url/url-type/{urlType}` - Filter by URL type

### 8. LinkedIn Post - Collect

**POST** `/linkedin/post-collect`

**Request Body:**
```json
{
  "urls": [
    "https://linkedin.com/posts/username_activity-*********",
    "https://linkedin.com/pulse/article-title-author-name"
  ]
}
```

**Validation Rules:**
- `urls`: Array of 1-50 LinkedIn post/pulse URLs
- Each URL must match pattern: `/linkedin\.com\/(posts|pulse)/`
- URL max length: 500 characters

**Success Response (200):**
```json
{
  "success": true,
  "message": "Post collection started successfully",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "urls_count": 2,
  "instructions": {
    "check_status": "GET /linkedin/post-collect/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/post-collect/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

**Additional Endpoints:**
- `GET /linkedin/post-collect` - Get all collected posts
- `GET /linkedin/post-collect/{id}` - Get specific post by ID

### 9. LinkedIn Post - Discover by Company

**POST** `/linkedin/post-discover-company`

**Request Body:**
```json
{
  "urls": [
    "https://linkedin.com/company/google",
    "https://linkedin.com/company/microsoft"
  ]
}
```

**Validation Rules:**
- `urls`: Array of 1-50 LinkedIn company URLs
- Each URL must match pattern: `/linkedin\.com\/company/`
- URL max length: 500 characters

**Success Response (200):**
```json
{
  "success": true,
  "message": "Company post discovery started successfully",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "urls_count": 2,
  "instructions": {
    "check_status": "GET /linkedin/post-discover-company/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/post-discover-company/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

**Additional Endpoints:**
- `GET /linkedin/post-discover-company` - Get all discovered posts
- `GET /linkedin/post-discover-company/{id}` - Get specific post by ID

### 10. LinkedIn Post - Discover by Profile

**POST** `/linkedin/post-discover-profile`

**Request Body:**
```json
{
  "profiles": [
    {
      "url": "https://linkedin.com/in/username",
      "start_date": "2024-01-01T00:00:00Z",
      "end_date": "2024-12-31T23:59:59Z"
    }
  ]
}
```

**Validation Rules:**
- `profiles`: Array of 1-50 profile objects
- `url`: Valid LinkedIn profile URL matching `/linkedin\.com\/in\//`
- `start_date`: ISO datetime string
- `end_date`: ISO datetime string
- URL max length: 500 characters

**Success Response (200):**
```json
{
  "success": true,
  "message": "Profile post discovery started successfully",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "profiles_count": 1,
  "instructions": {
    "check_status": "GET /linkedin/post-discover-profile/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/post-discover-profile/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

**Additional Endpoints:**
- `GET /linkedin/post-discover-profile` - Get all discovered posts
- `GET /linkedin/post-discover-profile/{id}` - Get specific post by ID

### 11. LinkedIn People Search - Collect

**POST** `/linkedin/people-search-collect`

**Request Body:**
```json
{
  "searches": [
    {
      "url": "https://linkedin.com/search/results/people/",
      "first_name": "John",
      "last_name": "Doe"
    }
  ]
}
```

**Validation Rules:**
- `searches`: Array of 1-10 search objects
- `url`: Valid LinkedIn search URL
- `first_name`: Required string min 1 character
- `last_name`: Required string min 1 character

**Success Response (200):**
```json
{
  "success": true,
  "message": "People search started successfully",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "searches_count": 1,
  "instructions": {
    "check_status": "GET /linkedin/people-search-collect/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/people-search-collect/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

**Additional Endpoints:**
- `GET /linkedin/people-search-collect` - Get all search results
- `GET /linkedin/people-search-collect/{id}` - Get specific result by ID

## Common Response Patterns

### Async Operation Response
All POST endpoints return an async operation response with snapshot_id for tracking:

```json
{
  "success": true,
  "message": "Operation started successfully",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "instructions": {
    "check_status": "GET /endpoint/snapshot/{snapshot_id}/status",
    "get_data": "GET /endpoint/snapshot/{snapshot_id}/data"
  }
}
```

### Status Check Response
```json
{
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "running|completed|failed",
  "progress": 75,
  "total_records": 100,
  "processed_records": 75,
  "estimated_completion": "2024-01-01T12:30:00Z"
}
```

### Data Retrieval Response
```json
{
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "completed",
  "total_records": 50,
  "data": [
    // Array of collected/discovered data objects
  ],
  "metadata": {
    "collection_date": "2024-01-01T12:00:00Z",
    "source": "brightdata",
    "user_id": "user-uuid"
  }
}
```

### List Response (GET endpoints)
```json
{
  "success": true,
  "data": [
    // Array of data objects
  ],
  "total": 150,
  "page": 1,
  "limit": 50,
  "metadata": {
    "user_id": "user-uuid",
    "last_updated": "2024-01-01T12:00:00Z"
  }
}
```

## Error Handling

### Validation Error (400)
```json
{
  "message": "Validation failed",
  "error": "Bad Request",
  "statusCode": 400,
  "issues": [
    {
      "code": "invalid_string",
      "message": "Must be a valid URL",
      "path": ["urls", 0]
    },
    {
      "code": "too_small",
      "message": "At least one URL is required",
      "path": ["urls"]
    }
  ]
}
```

### Authentication Error (401)
```json
{
  "message": "Unauthorized",
  "error": "Unauthorized",
  "statusCode": 401
}
```

### BrightData API Error (502)
```json
{
  "message": "BrightData API error: Rate limit exceeded",
  "error": "Bad Gateway",
  "statusCode": 502,
  "details": {
    "provider": "brightdata",
    "error_code": "RATE_LIMIT_EXCEEDED"
  }
}
```

### Server Error (500)
```json
{
  "message": "Internal server error",
  "error": "Internal Server Error",
  "statusCode": 500
}
```

## Data Models

### LinkedIn Post Data Structure
```json
{
  "id": "uuid",
  "linkedin_post_id": "post-123456",
  "url": "https://linkedin.com/posts/...",
  "user_id": "author-user-id",
  "post_type": "post|article|video",
  "date_posted": "2024-01-01T12:00:00Z",
  "title": "Post title",
  "post_text": "Post content text",
  "engagement": {
    "likes": 150,
    "comments": 25,
    "shares": 10,
    "reactions": 200
  },
  "media": [
    {
      "type": "image|video|document",
      "url": "https://media.licdn.com/...",
      "alt_text": "Media description"
    }
  ],
  "hashtags": ["#technology", "#innovation"],
  "tagged_users": [
    {
      "name": "John Doe",
      "url": "https://linkedin.com/in/johndoe"
    }
  ],
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

### LinkedIn Profile Data Structure
```json
{
  "id": "uuid",
  "linkedin_profile_id": "profile-123456",
  "url": "https://linkedin.com/in/username",
  "name": "John Doe",
  "headline": "Software Engineer at Company",
  "location": "San Francisco, CA",
  "summary": "Professional summary...",
  "experience": [
    {
      "title": "Software Engineer",
      "company": "Tech Company",
      "duration": "2020-Present",
      "description": "Job description..."
    }
  ],
  "education": [
    {
      "school": "University Name",
      "degree": "Bachelor of Science",
      "field": "Computer Science",
      "years": "2016-2020"
    }
  ],
  "skills": ["JavaScript", "Python", "React"],
  "connections": 500,
  "followers": 1000,
  "avatar_url": "https://media.licdn.com/...",
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

### LinkedIn Job Data Structure
```json
{
  "id": "uuid",
  "linkedin_job_id": "job-123456",
  "url": "https://linkedin.com/jobs/view/123456",
  "title": "Software Engineer",
  "company": "Tech Company",
  "location": "San Francisco, CA",
  "employment_type": "Full-time",
  "experience_level": "Mid-Senior level",
  "description": "Job description...",
  "requirements": ["Bachelor's degree", "5+ years experience"],
  "salary_range": "$100,000 - $150,000",
  "posted_date": "2024-01-01T12:00:00Z",
  "application_deadline": "2024-02-01T12:00:00Z",
  "applicants_count": 50,
  "remote_allowed": true,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

### LinkedIn Company Data Structure
```json
{
  "id": "uuid",
  "linkedin_company_id": "company-123456",
  "url": "https://linkedin.com/company/company-name",
  "name": "Tech Company",
  "industry": "Technology",
  "size": "1001-5000 employees",
  "headquarters": "San Francisco, CA",
  "founded": 2010,
  "description": "Company description...",
  "website": "https://company.com",
  "specialties": ["Software Development", "AI", "Cloud Computing"],
  "followers": 100000,
  "logo_url": "https://media.licdn.com/...",
  "cover_image_url": "https://media.licdn.com/...",
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

## Frontend Integration Examples

### React/JavaScript Example

```javascript
// Authentication setup
const API_BASE_URL = 'http://localhost:3000';
const supabaseToken = 'your-supabase-jwt-token';

const apiClient = {
  headers: {
    'Authorization': `Bearer ${supabaseToken}`,
    'Content-Type': 'application/json'
  }
};

// Discover LinkedIn posts
async function discoverLinkedInPosts(urls) {
  try {
    const response = await fetch(`${API_BASE_URL}/linkedin/post-discover-url`, {
      method: 'POST',
      headers: apiClient.headers,
      body: JSON.stringify({
        urls: urls.map(url => ({ url, limit: 50 }))
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error discovering posts:', error);
    throw error;
  }
}

// Check operation status
async function checkOperationStatus(snapshotId, endpoint) {
  try {
    const response = await fetch(
      `${API_BASE_URL}/${endpoint}/snapshot/${snapshotId}/status`,
      {
        method: 'GET',
        headers: apiClient.headers
      }
    );

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error checking status:', error);
    throw error;
  }
}

// Get operation data
async function getOperationData(snapshotId, endpoint) {
  try {
    const response = await fetch(
      `${API_BASE_URL}/${endpoint}/snapshot/${snapshotId}/data`,
      {
        method: 'GET',
        headers: apiClient.headers
      }
    );

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error getting data:', error);
    throw error;
  }
}

// Usage example
async function handlePostDiscovery() {
  try {
    // Start discovery
    const operation = await discoverLinkedInPosts([
      'https://linkedin.com/in/username'
    ]);

    console.log('Operation started:', operation.snapshot_id);

    // Poll for completion
    let status = 'started';
    while (status !== 'completed' && status !== 'failed') {
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds

      const statusResult = await checkOperationStatus(
        operation.snapshot_id,
        'linkedin/post-discover-url'
      );

      status = statusResult.status;
      console.log(`Status: ${status}, Progress: ${statusResult.progress}%`);
    }

    if (status === 'completed') {
      // Get the data
      const data = await getOperationData(
        operation.snapshot_id,
        'linkedin/post-discover-url'
      );

      console.log('Discovered posts:', data.data);
      return data.data;
    } else {
      throw new Error('Operation failed');
    }
  } catch (error) {
    console.error('Discovery failed:', error);
    throw error;
  }
}
```

## Best Practices

### 1. Authentication
- Always include Bearer token in Authorization header
- Handle 401 errors by refreshing Supabase token
- Store tokens securely (not in localStorage for production)

### 2. Error Handling
- Check response status codes
- Parse error messages from response body
- Implement retry logic for 502 (BrightData) errors
- Show user-friendly error messages

### 3. Async Operations
- Always poll status before attempting to get data
- Implement reasonable polling intervals (5-10 seconds)
- Show progress indicators to users
- Handle timeout scenarios gracefully

### 4. Data Management
- Cache responses appropriately
- Implement pagination for large datasets
- Store snapshot_ids for later data retrieval
- Handle user data isolation automatically

### 5. Rate Limiting
- Respect API rate limits
- Implement exponential backoff for retries
- Batch requests when possible
- Monitor BrightData quota usage

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check Supabase token validity
   - Ensure Bearer token format is correct
   - Verify token hasn't expired

2. **400 Validation Error**
   - Check request body format matches schemas
   - Verify URL patterns match requirements
   - Ensure required fields are present

3. **502 BrightData Error**
   - Check BrightData service status
   - Verify dataset IDs are configured
   - Implement retry logic with backoff

4. **Timeout Issues**
   - Increase polling intervals
   - Check operation status regularly
   - Handle long-running operations gracefully

### Support
- Swagger Documentation: `http://localhost:3000/api-docs`
- Check server logs for detailed error information
- Verify environment variables are properly configured
- Test with smaller datasets first
