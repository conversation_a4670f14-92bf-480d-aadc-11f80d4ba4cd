{"openapi": "3.0.0", "info": {"title": "LinkedIn People Search Collection API", "description": "API for searching and collecting LinkedIn people by search criteria (name-based search)", "version": "1.0.0"}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}], "security": [{"bearerAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"LinkedInPeopleSearchRequestDto": {"type": "object", "required": ["searches"], "properties": {"searches": {"type": "array", "items": {"type": "object", "required": ["url", "first_name", "last_name"], "properties": {"url": {"type": "string", "format": "uri", "description": "LinkedIn base URL for search", "example": "https://www.linkedin.com"}, "first_name": {"type": "string", "minLength": 1, "description": "First name to search for", "example": "james"}, "last_name": {"type": "string", "minLength": 1, "description": "Last name to search for", "example": "smith"}}}, "minItems": 1, "maxItems": 10, "description": "Array of search criteria (max 10 searches per request)"}}}, "LinkedInPersonDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Database record ID"}, "name": {"type": "string", "description": "Full name of the person", "example": "<PERSON>"}, "subtitle": {"type": "string", "description": "Professional subtitle or job title", "example": "Senior Software Engineer at Google"}, "location": {"type": "string", "description": "Geographic location", "example": "United Kingdom"}, "experience": {"type": "string", "description": "Current or recent work experience", "example": "Corporate Sales"}, "education": {"type": "string", "description": "Educational background", "example": "Stanford University"}, "avatar": {"type": "string", "format": "uri", "description": "Profile picture URL"}, "url": {"type": "string", "format": "uri", "description": "LinkedIn profile URL", "example": "https://www.linkedin.com/in/james-smith-123/"}, "search_first_name": {"type": "string", "description": "First name used in search", "example": "james"}, "search_last_name": {"type": "string", "description": "Last name used in search", "example": "smith"}, "search_url": {"type": "string", "format": "uri", "description": "URL used in search", "example": "https://www.linkedin.com"}, "input_url": {"type": "string", "format": "uri", "description": "Original input URL"}, "timestamp": {"type": "string", "description": "Timestamp when data was collected", "example": "2024-01-15T10:30:00Z"}, "created_at": {"type": "string", "format": "date-time", "description": "Record creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Record last update timestamp"}}}, "LinkedInPeopleSearchResponseDto": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Whether the operation was successful", "example": true}, "message": {"type": "string", "description": "Response message", "example": "LinkedIn people search collection started successfully. Use the snapshot_id to check status and retrieve data when ready."}, "snapshot_id": {"type": "string", "description": "Snapshot ID for tracking async operations", "example": "s_mdea2wuk1dr7b21l7r"}, "status": {"type": "string", "description": "Current status of the operation", "example": "started"}, "searches_count": {"type": "integer", "minimum": 0, "description": "Number of searches processed", "example": 2}, "instructions": {"type": "object", "properties": {"check_status": {"type": "string", "description": "Endpoint to check operation status", "example": "GET /linkedin/people-search-collect/snapshot/s_mdea2wuk1dr7b21l7r/status"}, "get_data": {"type": "string", "description": "Endpoint to retrieve collected data", "example": "GET /linkedin/people-search-collect/snapshot/s_mdea2wuk1dr7b21l7r/data"}}, "description": "Instructions for next steps"}}}, "PaginatedLinkedInPeopleDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LinkedInPersonDto"}, "description": "Array of LinkedIn people"}, "total": {"type": "integer", "minimum": 0, "description": "Total number of people", "example": 150}, "page": {"type": "integer", "minimum": 1, "description": "Current page number", "example": 1}, "limit": {"type": "integer", "minimum": 1, "description": "Number of items per page", "example": 10}, "totalPages": {"type": "integer", "minimum": 0, "description": "Total number of pages", "example": 15}}}, "SearchCriteriaDto": {"type": "object", "properties": {"first_name": {"type": "string", "description": "First name filter", "example": "james"}, "last_name": {"type": "string", "description": "Last name filter", "example": "smith"}, "location": {"type": "string", "description": "Location filter", "example": "United Kingdom"}, "experience": {"type": "string", "description": "Experience filter", "example": "Corporate Sales"}}}, "SnapshotStatusResponse": {"type": "object", "properties": {"snapshot_id": {"type": "string", "example": "s_mdea2wuk1dr7b21l7r"}, "status": {"type": "string", "enum": ["running", "completed", "ready", "failed", "error"], "example": "running"}, "progress": {"type": "integer", "minimum": 0, "maximum": 100, "example": 50}}}, "ErrorResponse": {"type": "object", "properties": {"message": {"type": "string", "description": "Error message"}, "error": {"type": "string", "description": "Error type"}, "statusCode": {"type": "integer", "description": "HTTP status code"}}}}}, "paths": {"/linkedin/people-search-collect": {"post": {"tags": ["LinkedIn People Search Collection"], "summary": "Collect LinkedIn people by search criteria", "description": "Trigger LinkedIn people search collection using BrightData API with search criteria (URL, first name, last name). This endpoint starts an ASYNC operation and returns a snapshot_id for monitoring progress.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinkedInPeopleSearchRequestDto"}, "examples": {"single_search": {"summary": "Single search example", "value": {"searches": [{"url": "https://www.linkedin.com", "first_name": "james", "last_name": "smith"}]}}, "multiple_searches": {"summary": "Multiple searches example", "value": {"searches": [{"url": "https://www.linkedin.com", "first_name": "james", "last_name": "smith"}, {"url": "https://www.linkedin.com", "first_name": "<PERSON>", "last_name": "Ledger"}]}}}}}}, "responses": {"200": {"description": "People search collection started successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinkedInPeopleSearchResponseDto"}}}}, "400": {"description": "Bad request - Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"validation_error": {"summary": "Validation error", "value": {"statusCode": 400, "message": ["searches must contain at least 1 elements"], "error": "Bad Request"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"config_error": {"summary": "Configuration error", "value": {"statusCode": 500, "message": "LinkedIn People Search Collect dataset ID is not configured", "error": "Internal Server Error"}}}}}}}}, "get": {"tags": ["LinkedIn People Search Collection"], "summary": "Get all collected people", "description": "Retrieve all LinkedIn people that have been collected and stored in the database with pagination.", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "required": false, "description": "Page number (default: 1)", "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "required": false, "description": "Number of items per page (default: 10, max: 100)", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}], "responses": {"200": {"description": "People retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedLinkedInPeopleDto"}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/linkedin/people-search-collect/search": {"get": {"tags": ["LinkedIn People Search Collection"], "summary": "Search people by criteria", "description": "Search collected LinkedIn people using various criteria like first name, last name, location, or experience.", "parameters": [{"name": "first_name", "in": "query", "required": false, "description": "Filter by first name (partial match)", "schema": {"type": "string", "example": "james"}}, {"name": "last_name", "in": "query", "required": false, "description": "Filter by last name (partial match)", "schema": {"type": "string", "example": "smith"}}, {"name": "location", "in": "query", "required": false, "description": "Filter by location (partial match)", "schema": {"type": "string", "example": "United Kingdom"}}, {"name": "experience", "in": "query", "required": false, "description": "Filter by experience (partial match)", "schema": {"type": "string", "example": "Corporate Sales"}}], "responses": {"200": {"description": "People retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LinkedInPersonDto"}}}}}}}}, "/linkedin/people-search-collect/location/{location}": {"get": {"tags": ["LinkedIn People Search Collection"], "summary": "Get people by location", "description": "Retrieve all LinkedIn people from a specific location.", "parameters": [{"name": "location", "in": "path", "required": true, "description": "Location to filter by", "schema": {"type": "string", "example": "United Kingdom"}}], "responses": {"200": {"description": "People retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LinkedInPersonDto"}}}}}}}}, "/linkedin/people-search-collect/{id}": {"get": {"tags": ["LinkedIn People Search Collection"], "summary": "Get person by ID", "description": "Retrieve a specific LinkedIn person by their database ID.", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Database ID of the person", "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Person retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinkedInPersonDto"}}}}, "404": {"description": "Person not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"not_found": {"summary": "Person not found", "value": {"statusCode": 404, "message": "Person with ID 123e4567-e89b-12d3-a456-************ not found", "error": "Not Found"}}}}}}}}, "patch": {"tags": ["LinkedIn People Search Collection"], "summary": "Update person information", "description": "Update specific fields of a LinkedIn person record.", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Database ID of the person", "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>"}, "subtitle": {"type": "string", "example": "Senior Software Engineer"}, "location": {"type": "string", "example": "San Francisco, CA"}, "experience": {"type": "string", "example": "Google Inc."}, "education": {"type": "string", "example": "Stanford University"}}}}}}, "responses": {"200": {"description": "Person updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinkedInPersonDto"}}}}, "404": {"description": "Person not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["LinkedIn People Search Collection"], "summary": "Delete person record", "description": "Delete a LinkedIn person record from the database.", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Database ID of the person", "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Person deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Person deleted successfully"}}}}}}, "404": {"description": "Person not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/linkedin/people-search-collect/snapshot/{snapshotId}/status": {"get": {"tags": ["LinkedIn People Search Collection"], "summary": "Check snapshot status", "description": "Check the status of a LinkedIn people search collection operation using the snapshot ID.", "parameters": [{"name": "snapshotId", "in": "path", "required": true, "description": "The snapshot ID returned from the collection request", "schema": {"type": "string", "example": "s_mdea2wuk1dr7b21l7r"}}], "responses": {"200": {"description": "Snapshot status retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SnapshotStatusResponse"}}}}, "404": {"description": "Snapshot not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"not_found": {"summary": "Snapshot not found", "value": {"statusCode": 404, "message": "Snapshot not found", "error": "Not Found"}}}}}}}}}, "/linkedin/people-search-collect/snapshot/{snapshotId}/data": {"get": {"tags": ["LinkedIn People Search Collection"], "summary": "Get snapshot data", "description": "Retrieve the collected LinkedIn people data from a completed snapshot.", "security": [{"bearerAuth": []}], "parameters": [{"name": "snapshotId", "in": "path", "required": true, "description": "The snapshot ID returned from the collection request", "schema": {"type": "string", "example": "s_mdea2wuk1dr7b21l7r"}}], "responses": {"200": {"description": "Snapshot data retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LinkedInPersonDto"}}}}}, "404": {"description": "Snapshot not found or no data available", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"not_found": {"summary": "Snapshot data not found", "value": {"statusCode": 404, "message": "Snapshot data not found", "error": "Not Found"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}}