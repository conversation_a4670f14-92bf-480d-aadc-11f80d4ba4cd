{"openapi": "3.0.0", "info": {"title": "LinkedIn People Profile Collection API", "description": "API for collecting detailed LinkedIn profile information by URLs", "version": "1.0.0"}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}], "security": [{"bearerAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"LinkedInUrlDto": {"type": "object", "required": ["urls"], "properties": {"urls": {"type": "array", "items": {"type": "string", "format": "uri", "pattern": "linkedin\\.com\\/in\\/", "example": "https://www.linkedin.com/in/elad-moshe-05a90413/"}, "minItems": 1, "maxItems": 100, "description": "Array of LinkedIn profile URLs to collect information from"}}}, "PeopleProfile": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Database record ID"}, "linkedin_num_id": {"type": "string", "description": "LinkedIn numerical ID", "example": "905328471"}, "url": {"type": "string", "format": "uri", "description": "LinkedIn profile URL", "example": "https://www.linkedin.com/in/praneeth-devarasetty/"}, "name": {"type": "string", "description": "Full name", "example": "<PERSON>raneeth Devarasetty"}, "first_name": {"type": "string", "description": "First name", "example": "Praneeth"}, "last_name": {"type": "string", "description": "Last name", "example": "Devarasetty"}, "country_code": {"type": "string", "description": "Country code", "example": "US"}, "city": {"type": "string", "description": "City location", "example": "San Francisco, CA"}, "about": {"type": "string", "description": "About section text", "example": "Software Engineer passionate about building scalable systems..."}, "followers": {"type": "integer", "description": "Number of followers", "example": 1200}, "connections": {"type": "integer", "description": "Number of connections", "example": 500}, "position": {"type": "string", "description": "Current position", "example": "Senior Software Engineer at Google"}, "experience": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "company": {"type": "string"}, "duration": {"type": "string"}, "location": {"type": "string"}, "description": {"type": "string"}}}, "description": "Work experience array"}, "current_company": {"type": "object", "properties": {"name": {"type": "string"}, "company_id": {"type": "string"}, "url": {"type": "string"}}, "description": "Current company information"}, "current_company_name": {"type": "string", "description": "Current company name", "example": "Google"}, "current_company_company_id": {"type": "string", "description": "Current company LinkedIn ID"}, "posts": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "date": {"type": "string"}, "reactions": {"type": "number"}, "comments": {"type": "number"}}}, "description": "Recent posts"}, "activity": {"type": "array", "items": {"type": "object"}, "description": "Recent activity"}, "education": {"type": "array", "items": {"type": "object", "properties": {"school": {"type": "string"}, "degree": {"type": "string"}, "field": {"type": "string"}, "years": {"type": "string"}}}, "description": "Education history"}, "educations_details": {"type": "string", "description": "Education details text"}, "courses": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "institution": {"type": "string"}}}, "description": "Courses taken"}, "certifications": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "issuer": {"type": "string"}, "date": {"type": "string"}}}, "description": "Certifications"}, "honors_and_awards": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "issuer": {"type": "string"}, "date": {"type": "string"}}}, "description": "Honors and awards"}, "volunteer_experience": {"type": "array", "items": {"type": "object", "properties": {"role": {"type": "string"}, "organization": {"type": "string"}, "duration": {"type": "string"}}}, "description": "Volunteer experience"}, "organizations": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "role": {"type": "string"}}}, "description": "Organizations"}, "recommendations_count": {"type": "integer", "description": "Number of recommendations"}, "recommendations": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "recommender": {"type": "string"}}}, "description": "Recommendations"}, "languages": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "proficiency": {"type": "string"}}}, "description": "Languages"}, "projects": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "url": {"type": "string"}}}, "description": "Projects"}, "patents": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "number": {"type": "string"}, "date": {"type": "string"}}}, "description": "Patents"}, "publications": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "publisher": {"type": "string"}, "date": {"type": "string"}}}, "description": "Publications"}, "avatar": {"type": "string", "format": "uri", "description": "Profile picture URL"}, "default_avatar": {"type": "boolean", "description": "Whether using default avatar"}, "banner_image": {"type": "string", "format": "uri", "description": "Banner image URL"}, "similar_profiles": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string"}, "title": {"type": "string"}}}, "description": "Similar profiles"}, "people_also_viewed": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string"}, "title": {"type": "string"}}}, "description": "People also viewed"}, "memorialized_account": {"type": "boolean", "description": "Whether account is memorialized"}, "input_url": {"type": "string", "format": "uri", "description": "Original input URL"}, "linkedin_id": {"type": "string", "description": "LinkedIn ID"}, "bio_links": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string"}, "title": {"type": "string"}}}, "description": "Bio links"}, "timestamp": {"type": "string", "description": "Collection timestamp", "example": "2025-07-20"}, "created_at": {"type": "string", "format": "date-time", "description": "Record creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Record last update timestamp"}}}, "CollectResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Data collection started successfully. Use the snapshot_id to check status and retrieve data when ready."}, "snapshot_id": {"type": "string", "example": "s_mdboahmo240821rs2a"}, "status": {"type": "string", "example": "started"}, "instructions": {"type": "object", "properties": {"check_status": {"type": "string", "example": "GET /linkedin/people-profile/collect/snapshot/s_mdboahmo240821rs2a/status"}, "get_data": {"type": "string", "example": "GET /linkedin/people-profile/collect/snapshot/s_mdboahmo240821rs2a/data"}}}}}, "SnapshotStatusResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "snapshot_id": {"type": "string", "example": "s_mdboahmo240821rs2a"}, "status": {"type": "string", "enum": ["running", "completed", "ready", "failed", "error"], "example": "completed"}, "dataset_id": {"type": "string", "example": "gd_l1viktl72bvl7bjuj0"}, "message": {"type": "string", "example": "Snapshot status: completed"}}}, "SnapshotDataResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "snapshot_id": {"type": "string", "example": "s_mdboahmo240821rs2a"}, "status": {"type": "string", "example": "completed"}, "message": {"type": "string", "example": "Successfully retrieved 1 profiles"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PeopleProfile"}}, "saved_count": {"type": "number", "example": 1}}}, "ErrorResponse": {"type": "object", "properties": {"message": {"type": "string", "description": "Error message"}, "error": {"type": "string", "description": "Error type"}, "statusCode": {"type": "integer", "description": "HTTP status code"}}}}}, "paths": {"/linkedin/people-profile/collect": {"post": {"tags": ["LinkedIn People Profile Collection"], "summary": "Collect LinkedIn profiles by URLs", "description": "Trigger data collection for LinkedIn profiles using BrightData API. This is an ASYNC operation that returns a snapshot_id for monitoring progress.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinkedInUrlDto"}, "examples": {"single_profile": {"summary": "Single profile URL", "value": {"urls": ["https://www.linkedin.com/in/elad-moshe-05a90413/"]}}, "multiple_profiles": {"summary": "Multiple profile URLs", "value": {"urls": ["https://www.linkedin.com/in/elad-moshe-05a90413/", "https://www.linkedin.com/in/jonathan-myrvik-3baa01109/", "https://www.linkedin.com/in/aviv-tal-75b81/"]}}}}}}, "responses": {"200": {"description": "Data collection started successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectResponse"}}}}, "400": {"description": "Invalid request body or URL format", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "502": {"description": "BrightData API error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "get": {"tags": ["LinkedIn People Profile Collection"], "summary": "Get all collected profiles", "description": "Retrieve all LinkedIn profiles stored in the database for the authenticated user", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Profiles retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PeopleProfile"}}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/linkedin/people-profile/collect/{id}": {"get": {"tags": ["LinkedIn People Profile Collection"], "summary": "Get profile by ID", "description": "Retrieve a specific LinkedIn profile by its database ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Database record ID (UUID)", "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PeopleProfile"}}}}, "404": {"description": "Profile not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/linkedin/people-profile/collect/linkedin-id/{linkedinId}": {"get": {"tags": ["LinkedIn People Profile Collection"], "summary": "Get profile by LinkedIn ID", "description": "Retrieve a specific LinkedIn profile by its LinkedIn ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "linkedinId", "in": "path", "required": true, "description": "LinkedIn numerical ID", "schema": {"type": "string", "example": "905328471"}}], "responses": {"200": {"description": "Profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PeopleProfile"}}}}, "404": {"description": "Profile not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/linkedin/people-profile/collect/snapshot/{snapshotId}/status": {"get": {"tags": ["LinkedIn People Profile Collection"], "summary": "Check snapshot status", "description": "Check the status of a BrightData snapshot collection. Possible statuses: running, completed, ready, failed, error", "parameters": [{"name": "snapshotId", "in": "path", "required": true, "description": "Snapshot ID returned from collection request", "schema": {"type": "string", "example": "s_mdboahmo240821rs2a"}}], "responses": {"200": {"description": "Snapshot status retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SnapshotStatusResponse"}}}}, "502": {"description": "BrightData API error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/linkedin/people-profile/collect/snapshot/{snapshotId}/data": {"get": {"tags": ["LinkedIn People Profile Collection"], "summary": "Get snapshot data", "description": "Retrieve the actual profile data from a completed BrightData snapshot. Only works when status is 'completed' or 'ready'.", "security": [{"bearerAuth": []}], "parameters": [{"name": "snapshotId", "in": "path", "required": true, "description": "Snapshot ID returned from collection request", "schema": {"type": "string", "example": "s_mdboahmo240821rs2a"}}], "responses": {"200": {"description": "Snapshot data retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SnapshotDataResponse"}}}}, "200 (not ready)": {"description": "Snapshot not ready yet", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "snapshot_id": {"type": "string", "example": "s_mdboahmo240821rs2a"}, "status": {"type": "string", "example": "running"}, "message": {"type": "string", "example": "Snapshot is not ready yet. Current status: running"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "502": {"description": "BrightData API error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}}