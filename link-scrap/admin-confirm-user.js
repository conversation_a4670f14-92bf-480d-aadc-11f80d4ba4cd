// This script requires your Supabase Service Role Key
// Get it from: https://supabase.com/dashboard/project/rfwtvklkxaftgelqmxuv/settings/api

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://rfwtvklkxaftgelqmxuv.supabase.co';

// Replace this with your actual Service Role Key from Supabase Dashboard
const serviceRoleKey = 'YOUR_SERVICE_ROLE_KEY_HERE';

async function adminConfirmUser() {
  if (serviceRoleKey === 'YOUR_SERVICE_ROLE_KEY_HERE') {
    console.log('❌ Please replace YOUR_SERVICE_ROLE_KEY_HERE with your actual service role key');
    console.log('🔗 Get it from: https://supabase.com/dashboard/project/rfwtvklkxaftgelqmxuv/settings/api');
    console.log('⚠️  Look for "service_role" key (not anon key)');
    return;
  }

  const supabase = createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  try {
    console.log('🔧 Using admin privileges to confirm user...');
    
    const userId = '2a1e6408-ce2c-4174-9d40-098e80c6c632';
    
    // Update user to confirm email
    const { data, error } = await supabase.auth.admin.updateUserById(userId, {
      email_confirm: true
    });

    if (error) {
      console.error('❌ Admin confirmation failed:', error.message);
      return;
    }

    console.log('✅ User confirmed successfully!');
    console.log('👤 User data:', data.user);
    
    // Now try to generate a session
    console.log('\n🎫 Generating JWT token...');
    
    const { data: sessionData, error: sessionError } = await supabase.auth.admin.generateLink({
      type: 'magiclink',
      email: '<EMAIL>'
    });

    if (sessionError) {
      console.error('❌ Token generation failed:', sessionError.message);
    } else {
      console.log('✅ Magic link generated:', sessionData);
    }

  } catch (err) {
    console.error('💥 Unexpected error:', err.message);
  }
}

console.log('🔑 Admin User Confirmation Script');
console.log('📋 Instructions:');
console.log('1. Go to: https://supabase.com/dashboard/project/rfwtvklkxaftgelqmxuv/settings/api');
console.log('2. Copy the "service_role" key (NOT the anon key)');
console.log('3. Replace YOUR_SERVICE_ROLE_KEY_HERE in this file');
console.log('4. Run this script again');
console.log('');

adminConfirmUser();
