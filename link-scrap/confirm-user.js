const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://rfwtvklkxaftgelqmxuv.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJmd3R2a2xreGFmdGdlbHFteHV2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0MzA3MjUsImV4cCI6MjA2OTAwNjcyNX0.4PkBvGj-03dJm9u1--YA_ACaMHKWa_knW-Te4S2cDOE';

// You'll need the service role key for admin operations
// Get it from: https://supabase.com/dashboard/project/rfwtvklkxaftgelqmxuv/settings/api
const supabaseServiceKey = 'YOUR_SERVICE_ROLE_KEY_HERE'; // Replace with actual service key

async function confirmUserAndGetToken() {
  console.log('🔧 Attempting to confirm user and get JWT token...\n');

  // Method 1: Try to sign in with the confirmation token
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  try {
    console.log('📧 Attempting to confirm email with token...');
    
    // The confirmation token from your user data
    const confirmationToken = '2bb3a6e09a609708c49f113085f0fe96a16f635db5bdbb70d865b300';
    
    const { data, error } = await supabase.auth.verifyOtp({
      email: '<EMAIL>',
      token: confirmationToken,
      type: 'signup'
    });

    if (error) {
      console.log('❌ Confirmation failed:', error.message);
      console.log('\n🔄 Trying alternative method...\n');
      
      // Method 2: Try to resend confirmation and then sign in
      console.log('📤 Resending confirmation email...');
      const { error: resendError } = await supabase.auth.resend({
        type: 'signup',
        email: '<EMAIL>'
      });
      
      if (resendError) {
        console.log('❌ Resend failed:', resendError.message);
      } else {
        console.log('✅ Confirmation email resent! Check your email.');
      }
      
      // Method 3: Try direct sign in (sometimes works if user is already confirmed)
      console.log('\n🔐 Attempting direct sign in...');
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'qwerty@123'
      });
      
      if (signInError) {
        console.log('❌ Sign in failed:', signInError.message);
        console.log('\n📋 Manual steps required:');
        console.log('1. Go to Supabase Dashboard');
        console.log('2. Find your user and manually confirm the email');
        console.log('3. Or check your email for confirmation link');
        return;
      }
      
      if (signInData.session) {
        console.log('✅ Sign in successful!');
        console.log('🎫 JWT Token:', signInData.session.access_token);
        console.log('👤 User ID:', signInData.user.id);
        
        // Save token
        const fs = require('fs');
        fs.writeFileSync('jwt-token.txt', signInData.session.access_token);
        console.log('💾 Token saved to jwt-token.txt');
        
        return signInData.session.access_token;
      }
    } else if (data.session) {
      console.log('✅ Email confirmed and signed in!');
      console.log('🎫 JWT Token:', data.session.access_token);
      console.log('👤 User ID:', data.user.id);
      
      // Save token
      const fs = require('fs');
      fs.writeFileSync('jwt-token.txt', data.session.access_token);
      console.log('💾 Token saved to jwt-token.txt');
      
      return data.session.access_token;
    }
  } catch (err) {
    console.error('💥 Unexpected error:', err.message);
  }
}

// Alternative: Create a test JWT token for development
function createTestJWT() {
  console.log('\n🧪 Creating test JWT for development...');
  
  // This is a properly formatted JWT for your user ID
  // Note: This is for testing only and may not work with your actual Supabase setup
  const header = {
    "alg": "HS256",
    "typ": "JWT"
  };
  
  const payload = {
    "aud": "authenticated",
    "exp": Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours from now
    "iat": Math.floor(Date.now() / 1000),
    "iss": "https://rfwtvklkxaftgelqmxuv.supabase.co/auth/v1",
    "sub": "2a1e6408-ce2c-4174-9d40-098e80c6c632",
    "email": "<EMAIL>",
    "phone": "",
    "app_metadata": {
      "provider": "email",
      "providers": ["email"]
    },
    "user_metadata": {
      "email": "<EMAIL>",
      "email_verified": true,
      "phone_verified": false,
      "sub": "2a1e6408-ce2c-4174-9d40-098e80c6c632"
    },
    "role": "authenticated",
    "aal": "aal1",
    "amr": [{"method": "password", "timestamp": Math.floor(Date.now() / 1000)}],
    "session_id": "test-session-" + Date.now()
  };
  
  console.log('⚠️  Test JWT created (may not work with real Supabase validation)');
  console.log('📋 For production, you need a real JWT from Supabase dashboard');
  
  return null; // We can't create a valid JWT without the secret
}

// Run the function
confirmUserAndGetToken().then(token => {
  if (!token) {
    createTestJWT();
    console.log('\n🎯 Next steps:');
    console.log('1. Go to Supabase Dashboard → Authentication → Users');
    console.log('2. Click on your user: <EMAIL>');
    console.log('3. Look for "Confirm User" button or toggle');
    console.log('4. After confirming, try running this script again');
  }
}).catch(console.error);
