# LinkedIn Job Listing Collection API

## Overview

This API provides comprehensive LinkedIn job listing data collection using BrightData integration. It supports collecting job information by URLs, managing async operations, and storing data in PostgreSQL.

## Features

- ✅ **Zod Validation**: Input validation using Zod schemas
- ✅ **BrightData Integration**: Seamless integration with BrightData API
- ✅ **Async Operations**: Support for snapshot-based async data collection
- ✅ **Database Storage**: PostgreSQL storage with TypeORM
- ✅ **Comprehensive Swagger**: Detailed API documentation
- ✅ **Error Handling**: Robust error handling and logging

## API Endpoints

### 1. Collect Job Listings
**POST** `/linkedin/job-listing/collect`

Trigger data collection for LinkedIn job listings using BrightData API.

**Request Body:**
```json
{
  "urls": [
    "https://www.linkedin.com/jobs/view/remote-typist-%E2%80%93-data-entry-specialist-work-from-home-at-cwa-group-4181034038?trk=public_jobs_topcard-title",
    "https://www.linkedin.com/jobs/view/arrt-r-at-shared-imaging-llc-4180989163?trk=public_jobs_topcard-title"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Data collection started successfully. Use the snapshot_id to check status and retrieve data when ready.",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "instructions": {
    "check_status": "GET /linkedin/job-listing/collect/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/job-listing/collect/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

### 2. Check Snapshot Status
**GET** `/linkedin/job-listing/collect/snapshot/{snapshotId}/status`

Check the status of a data collection snapshot.

**Response:**
```json
{
  "success": true,
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "ready",
  "dataset_id": "gd_lpfll7v5hcqtkxl6l",
  "message": "Snapshot status: ready"
}
```

### 3. Get Snapshot Data
**GET** `/linkedin/job-listing/collect/snapshot/{snapshotId}/data`

Retrieve and save job listing data from a completed snapshot.

**Response:**
```json
{
  "success": true,
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "ready",
  "message": "Successfully retrieved 2 job listings",
  "data": [
    {
      "url": "https://www.linkedin.com/jobs/view/planner-start-people-at-jobster-4270099119?_l=en",
      "job_posting_id": "4270099119",
      "job_title": "Planner - Start People",
      "company_name": "Jobster",
      "job_location": "Doetinchem, Gelderland, Netherlands",
      "job_employment_type": "Full-time",
      "job_num_applicants": 25
    }
  ],
  "saved_count": 2
}
```

### 4. Get All Job Listings
**GET** `/linkedin/job-listing/collect`

Retrieve all collected job listings from the database.

### 5. Get Job Listing by ID
**GET** `/linkedin/job-listing/collect/{id}`

Retrieve a specific job listing by its database ID.

### 6. Get Job Listing by Posting ID
**GET** `/linkedin/job-listing/collect/posting/{postingId}`

Retrieve a specific job listing by its LinkedIn posting ID.

## Data Structure

The API collects comprehensive job listing information including:

- **Basic Info**: Job title, company name, location, posting ID
- **Job Details**: Employment type, seniority level, industries, function
- **Application Info**: Number of applicants, application availability, apply link
- **Salary Info**: Base salary, pay range, salary standards
- **Content**: Job summary, formatted description
- **Company Info**: Company URL, logo, company ID
- **Metadata**: Posted date/time, discovery input, timestamps

## Environment Configuration

Required environment variables in `.env`:

```env
# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/linkscrap_db

# BrightData API Configuration
BRIGHTDATA_BASE_URL=https://api.brightdata.com/datasets/v3/trigger
BRIGHTDATA_API_KEY=fc120b5e2ea15b6cf99ba192e3e4b8db6514f0a836b7b4a91dc4f52aa064677a

# Dataset IDs
JOB_LISTING_COLLECT_DATASET_ID=gd_lpfll7v5hcqtkxl6l

# Application Configuration
NODE_ENV=development
PORT=3000
```

## Setup Instructions

1. **Install Dependencies:**
   ```bash
   npm install
   ```

2. **Start Database:**
   ```bash
   docker-compose up -d postgres
   ```

3. **Build Application:**
   ```bash
   npm run build
   ```

4. **Start Application:**
   ```bash
   npm run start:dev
   ```

5. **Access Swagger Documentation:**
   Open http://localhost:3000/api-docs

## Testing

The application builds successfully and includes:
- ✅ TypeScript compilation
- ✅ Zod validation schemas
- ✅ Swagger documentation
- ✅ Database entity definitions
- ✅ Service layer implementation
- ✅ Controller endpoints

## Architecture

- **Controller**: Handles HTTP requests and responses
- **Service**: Business logic and BrightData integration
- **Entity**: Database schema definition
- **DTO**: Input validation with Zod
- **Module**: Dependency injection configuration

## Error Handling

The API includes comprehensive error handling for:
- Invalid URLs
- BrightData API errors
- Database connection issues
- Missing configuration
- Async operation timeouts

## Logging

Detailed logging throughout the application for:
- Request/response tracking
- BrightData API interactions
- Database operations
- Error conditions
