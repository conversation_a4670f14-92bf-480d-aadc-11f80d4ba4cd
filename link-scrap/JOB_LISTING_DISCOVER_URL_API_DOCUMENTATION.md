# LinkedIn Job Listing Discovery by URL API

## Overview

This API provides comprehensive LinkedIn job listing discovery using URL-based search with BrightData integration. It supports discovering jobs from various LinkedIn job URLs including search pages, company job pages, and other job listing URLs.

## Features

- ✅ **Zod Validation**: Input validation using Zod schemas
- ✅ **BrightData Integration**: Seamless integration with BrightData discover_by=url API
- ✅ **Multiple URL Types**: Support for job search URLs, company job pages, and general job listing URLs
- ✅ **Async Operations**: Support for snapshot-based async data discovery
- ✅ **Database Storage**: PostgreSQL storage with URL metadata
- ✅ **Comprehensive Swagger**: Detailed API documentation
- ✅ **URL Metadata**: Store discovery URL and URL type with discovered jobs for better tracking

## API Endpoints

### 1. Discover Job Listings by URL
**POST** `/linkedin/job-listing/discover-url`

Trigger job discovery using BrightData API with LinkedIn job URLs.

**Request Body:**
```json
{
  "urls": [
    "https://www.linkedin.com/jobs/search?keywords=Software&location=Tel%20Aviv-Yafo&geoId=*********&trk=public_jobs_jobs-search-bar_search-submit&position=1&pageNum=0&f_TPR=r3600",
    "https://www.linkedin.com/jobs/semrush-jobs?f_C=2821922",
    "https://www.linkedin.com/jobs/reddit-inc.-jobs-worldwide?f_C=150573"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Job discovery by URL started successfully. Use the snapshot_id to check status and retrieve data when ready.",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "discovery_urls": 3,
  "instructions": {
    "check_status": "GET /linkedin/job-listing/discover-url/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/job-listing/discover-url/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

### 2. Check Snapshot Status
**GET** `/linkedin/job-listing/discover-url/snapshot/{snapshotId}/status`

Check the status of a job discovery by URL snapshot.

**Response:**
```json
{
  "success": true,
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "ready",
  "dataset_id": "gd_lpfll7v5hcqtkxl6l",
  "message": "Snapshot status: ready"
}
```

### 3. Get Snapshot Data
**GET** `/linkedin/job-listing/discover-url/snapshot/{snapshotId}/data`

Retrieve and save discovered job listing data from a completed snapshot.

**Response:**
```json
{
  "success": true,
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "ready",
  "message": "Successfully discovered 50 job listings from URLs",
  "data": [
    {
      "url": "https://www.linkedin.com/jobs/view/planner-start-people-at-jobster-4270099119?_l=en",
      "job_posting_id": "4270099119",
      "job_title": "Planner - Start People",
      "company_name": "Jobster",
      "job_location": "Doetinchem, Gelderland, Netherlands",
      "job_employment_type": "Full-time",
      "job_num_applicants": 25,
      "discovery_url": "https://www.linkedin.com/jobs/search?keywords=Software&location=Tel%20Aviv-Yafo",
      "discovery_url_type": "search"
    }
  ],
  "saved_count": 50
}
```

### 4. Get All Discovered Job Listings
**GET** `/linkedin/job-listing/discover-url`

Retrieve all discovered job listings from the database.

### 5. Get Discovered Job Listings by URL Type
**GET** `/linkedin/job-listing/discover-url/url-type/{urlType}`

Retrieve discovered job listings filtered by URL type (search, company, general).

### 6. Get Discovered Job Listing by ID
**GET** `/linkedin/job-listing/discover-url/{id}`

Retrieve a specific discovered job listing by its database ID.

### 7. Get Discovered Job Listing by Posting ID
**GET** `/linkedin/job-listing/discover-url/posting/{postingId}`

Retrieve a specific discovered job listing by its LinkedIn posting ID.

## Supported URL Types

The API supports various LinkedIn job URL types:

### 1. Job Search URLs
- **Pattern**: `/jobs/search?keywords=...&location=...`
- **Type**: `search`
- **Example**: `https://www.linkedin.com/jobs/search?keywords=Software&location=Tel%20Aviv-Yafo&geoId=*********&trk=public_jobs_jobs-search-bar_search-submit&position=1&pageNum=0&f_TPR=r3600`

### 2. Company Job Pages
- **Pattern**: `/jobs/{company-name}-jobs?f_C=...`
- **Type**: `company`
- **Examples**: 
  - `https://www.linkedin.com/jobs/semrush-jobs?f_C=2821922`
  - `https://www.linkedin.com/jobs/reddit-inc.-jobs-worldwide?f_C=150573`

### 3. General Job Listing URLs
- **Pattern**: Other LinkedIn job URLs
- **Type**: `general`
- **Example**: Any other LinkedIn job listing URL

## Data Structure

The API discovers comprehensive job listing information including:

- **Basic Info**: Job title, company name, location, posting ID
- **Job Details**: Employment type, seniority level, industries, function
- **Application Info**: Number of applicants, application availability, apply link
- **Salary Info**: Base salary, pay range, salary standards
- **Content**: Job summary, formatted description
- **Company Info**: Company URL, logo, company ID
- **URL Metadata**: Original discovery URL and URL type
- **Metadata**: Posted date/time, discovery input, timestamps

## URL Metadata Storage

Each discovered job listing includes the original URL metadata:

- `discovery_url`: The original URL used for discovery
- `discovery_url_type`: The type of URL used ('search', 'company', 'general')

## Environment Configuration

Required environment variables in `.env`:

```env
# Database Configuration
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# BrightData API Configuration
BRIGHTDATA_BASE_URL=https://api.brightdata.com/datasets/v3/trigger
BRIGHTDATA_API_KEY=fc120b5e2ea15b6cf99ba192e3e4b8db6514f0a836b7b4a91dc4f52aa064677a

# Dataset IDs
JOB_LISTING_DISCOVER_URL_DATASET_ID=gd_lpfll7v5hcqtkxl6l

# Application Configuration
NODE_ENV=development
PORT=3000
```

## Setup Instructions

1. **Install Dependencies:**
   ```bash
   npm install
   ```

2. **Build Application:**
   ```bash
   npm run build
   ```

3. **Start Application:**
   ```bash
   npm run start:dev
   ```

4. **Access Swagger Documentation:**
   Open http://localhost:3000/api-docs

## Testing

The application builds successfully and includes:
- ✅ TypeScript compilation
- ✅ Zod validation schemas
- ✅ Swagger documentation
- ✅ Database entity definitions
- ✅ Service layer implementation
- ✅ Controller endpoints
- ✅ BrightData integration

## Architecture

- **Controller**: Handles HTTP requests and responses
- **Service**: Business logic and BrightData integration
- **Entity**: Database schema definition with URL metadata
- **DTO**: Input validation with Zod
- **Module**: Dependency injection configuration

## Error Handling

The API includes comprehensive error handling for:
- Invalid URLs
- Non-LinkedIn URLs
- BrightData API errors
- Database connection issues
- Missing configuration
- Async operation timeouts

## Logging

Detailed logging throughout the application for:
- Request/response tracking
- BrightData API interactions
- Database operations
- URL metadata tracking
- Error conditions

## URL Validation

The API validates that all provided URLs:
- Are valid URLs
- Contain `linkedin.com/jobs` in the path
- Are less than 500 characters
- Maximum 50 URLs per request
