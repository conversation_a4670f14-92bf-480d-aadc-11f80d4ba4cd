# LinkedIn Post Discovery by Company URL API

## Overview

This API provides comprehensive LinkedIn post discovery using company URL-based data extraction with BrightData integration. It discovers posts published by specific companies using their LinkedIn company URLs, providing detailed engagement metrics, media content, and company metadata.

## Features

- ✅ **Zod Validation**: Input validation using Zod schemas
- ✅ **BrightData Integration**: Seamless integration with BrightData API for post discovery by company URL
- ✅ **Company-Based Discovery**: Discover posts published by specific companies
- ✅ **Async Operations**: Support for snapshot-based async data discovery
- ✅ **Database Storage**: PostgreSQL storage with comprehensive post data and company metadata
- ✅ **Comprehensive Swagger**: Detailed API documentation
- ✅ **Frontend-Optimized Response**: Refined response structure for frontend consumption
- ✅ **Rich Metadata**: Engagement metrics, media content, hashtags, tagged entities, and company information

## API Endpoints

### 1. Discover LinkedIn Posts by Company URL
**POST** `/linkedin/post-discover-company`

Trigger post discovery using BrightData API with LinkedIn company URLs.

**Request Body:**
```json
{
  "urls": [
    "https://www.linkedin.com/company/lanieri",
    "https://www.linkedin.com/company/effortel",
    "https://www.linkedin.com/company/green-philly"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "LinkedIn post discovery by company URL started successfully. Use the snapshot_id to check status and retrieve data when ready.",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "company_urls": 3,
  "instructions": {
    "check_status": "GET /linkedin/post-discover-company/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/post-discover-company/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

### 2. Check Snapshot Status
**GET** `/linkedin/post-discover-company/snapshot/{snapshotId}/status`

Check the status of a LinkedIn post discovery by company URL snapshot.

**Response:**
```json
{
  "success": true,
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "ready",
  "dataset_id": "gd_lyy3tktm25m4avu764",
  "message": "Snapshot status: ready"
}
```

### 3. Get Snapshot Data
**GET** `/linkedin/post-discover-company/snapshot/{snapshotId}/data`

Retrieve and save discovered LinkedIn post data from a completed snapshot.

**Response:**
```json
{
  "success": true,
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "ready",
  "message": "Successfully discovered 15 LinkedIn posts from company URLs",
  "data": [
    {
      "id": "7343546267496587264",
      "url": "https://www.linkedin.com/posts/university-college-of-aviation-malaysia-unicam_unicam-university-college-activity-7343546267496587264-BWV9",
      "post_type": "post",
      "date_posted": "2025-06-25T07:51:06.627Z",
      "title": "In UniCAM, we offer a wide range of programs...",
      "post_text": "In UniCAM, we offer a wide range of programs from Foundation, Diploma...",
      "hashtags": ["#UniCAM", "#university", "#college", "#education"],
      "embedded_links": [
        "http://aviation.edu.my/",
        "https://www.linkedin.com/feed/hashtag/unicam"
      ],
      "engagement": {
        "likes": 0,
        "comments": 0
      },
      "author": {
        "user_id": "university-college-of-aviation-malaysia-unicam",
        "account_type": "Organization",
        "followers": 57
      },
      "media": {
        "images": [
          "https://media.licdn.com/dms/image/v2/D5622AQGOsKaJ2-L69Q/feedshare-shrink_2048_1536/B56ZemEagFHQA8-/0/*************?e=**********&v=beta&t=8QF0gLiPUQzk1xA1eIkvZGopkM6-MCieD8rcGFTEslQ"
        ],
        "videos": null,
        "document_cover_image": null
      },
      "company_info": {
        "company_url": "https://www.linkedin.com/company/university-college-of-aviation-malaysia-unicam",
        "company_name": "University College of Aviation Malaysia Unicam"
      },
      "tagged_companies": [],
      "tagged_people": []
    }
  ],
  "saved_count": 15
}
```

### 4. Get All Discovered Posts
**GET** `/linkedin/post-discover-company`

Retrieve all discovered LinkedIn posts from the database.

### 5. Get Posts by Company Name
**GET** `/linkedin/post-discover-company/company/{companyName}`

Retrieve LinkedIn posts filtered by company name.

### 6. Get Posts by Account Type
**GET** `/linkedin/post-discover-company/account-type/{accountType}`

Retrieve LinkedIn posts filtered by account type (Organization, Individual, etc.).

### 7. Get Post by ID
**GET** `/linkedin/post-discover-company/{id}`

Retrieve a specific discovered LinkedIn post by its database ID.

### 8. Get Post by LinkedIn Post ID
**GET** `/linkedin/post-discover-company/post/{postId}`

Retrieve a specific discovered LinkedIn post by its LinkedIn post ID.

## Supported Company URL Types

The API supports various LinkedIn company URL formats:

### 1. Standard Company URLs
- **Pattern**: `/company/{company-name}`
- **Example**: `https://www.linkedin.com/company/microsoft`

### 2. Company URLs with Parameters
- **Pattern**: `/company/{company-name}?{parameters}`
- **Examples**: 
  - `https://www.linkedin.com/company/microsoft?trk=public_profile`
  - `https://www.linkedin.com/company/google?originalSubdomain=in`

### 3. Company URLs with Subdomains
- **Pattern**: `https://{subdomain}.linkedin.com/company/{company-name}`
- **Example**: `https://in.linkedin.com/company/microsoft`

## Data Structure

The API discovers comprehensive post information including:

### Basic Information
- Post ID, URL, type, and posting date
- Title, headline, and post text (both plain and HTML)
- Company information and metadata

### Engagement Metrics
- Number of likes and comments
- Top visible comments
- User followers and connections

### Content Analysis
- Hashtags extraction
- Embedded links identification
- Tagged companies and people

### Media Content
- Images and videos
- Video duration and thumbnails
- Document attachments with page count

### Company Metadata
- Company URL and extracted company name
- Company-specific post categorization
- Organization account type information

## Frontend-Optimized Response

The API provides a refined response structure optimized for frontend consumption:

```json
{
  "id": "post_id",
  "url": "post_url",
  "post_type": "post|article",
  "date_posted": "ISO_date",
  "title": "post_title",
  "post_text": "post_content",
  "hashtags": ["#tag1", "#tag2"],
  "embedded_links": ["url1", "url2"],
  "engagement": {
    "likes": 29,
    "comments": 0
  },
  "author": {
    "user_id": "user_identifier",
    "account_type": "Organization|Individual",
    "followers": 128845
  },
  "media": {
    "images": ["image_url1", "image_url2"],
    "videos": "video_data",
    "document_cover_image": "document_url"
  },
  "company_info": {
    "company_url": "https://www.linkedin.com/company/company-name",
    "company_name": "Company Name"
  },
  "tagged_companies": [{"name": "Company", "link": "url"}],
  "tagged_people": [{"name": "Person", "link": "url"}]
}
```

## Environment Configuration

Required environment variables in `.env`:

```env
# Database Configuration
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# BrightData API Configuration
BRIGHTDATA_BASE_URL=https://api.brightdata.com/datasets/v3/trigger
BRIGHTDATA_API_KEY=fc120b5e2ea15b6cf99ba192e3e4b8db6514f0a836b7b4a91dc4f52aa064677a

# Dataset IDs
LINKEDIN_POST_DISCOVER_COMPANY_DATASET_ID=gd_lyy3tktm25m4avu764

# Application Configuration
NODE_ENV=development
PORT=3000
```

## Setup Instructions

1. **Install Dependencies:**
   ```bash
   npm install
   ```

2. **Build Application:**
   ```bash
   npm run build
   ```

3. **Start Application:**
   ```bash
   npm run start:dev
   ```

4. **Access Swagger Documentation:**
   Open http://localhost:3001/api-docs

## Testing

The application builds successfully and includes:
- ✅ TypeScript compilation
- ✅ Zod validation schemas
- ✅ Swagger documentation
- ✅ Database entity definitions
- ✅ Service layer implementation
- ✅ Controller endpoints
- ✅ BrightData integration

## Architecture

- **Controller**: Handles HTTP requests and responses with comprehensive Swagger documentation
- **Service**: Business logic and BrightData integration with company URL discovery
- **Entity**: Database schema definition with comprehensive post data and company metadata
- **DTO**: Input validation with Zod for LinkedIn company URLs
- **Module**: Dependency injection configuration with TypeORM and BrightData modules

## Error Handling

The API includes comprehensive error handling for:
- Invalid URLs (non-LinkedIn company URLs or malformed URLs)
- BrightData API errors and timeouts
- Database connection issues
- Missing configuration variables
- Async operation failures

## Logging

Detailed logging throughout the application for:
- Request/response tracking
- BrightData API interactions
- Database operations
- Company URL processing
- Data transformation processes
- Error conditions and debugging

## URL Validation

The API validates that all provided URLs:
- Are valid URLs
- Contain `linkedin.com/company` in the path
- Are less than 500 characters
- Maximum 50 URLs per request

## Complete LinkedIn API Suite

The application now includes **EIGHT** comprehensive LinkedIn APIs:

1. **People Profile Collection** (`/linkedin/people-profile/collect`) - Collect profiles by specific URLs
2. **People Profile Discovery** (`/linkedin/people-profile/discover`) - Discover profiles by search parameters
3. **Company Information Collection** (`/linkedin/company-info/collect`) - Collect company data
4. **Job Listing Collection** (`/linkedin/job-listing/collect`) - Collect jobs by specific URLs
5. **Job Listing Discovery by Keyword** (`/linkedin/job-listing/discover-keyword`) - Discover jobs by search parameters
6. **Job Listing Discovery by URL** (`/linkedin/job-listing/discover-url`) - Discover jobs from LinkedIn job pages
7. **Post Collection** (`/linkedin/post-collect`) - Collect posts and articles by URLs
8. **Post Discovery by Company URL** (`/linkedin/post-discover-company`) - Discover posts by company URLs

All APIs are fully functional, properly documented, tested, and ready for frontend integration with comprehensive BrightData integration and database storage.
