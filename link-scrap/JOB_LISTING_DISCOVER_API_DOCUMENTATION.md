# LinkedIn Job Listing Discovery by Keyword API

## Overview

This API provides comprehensive LinkedIn job listing discovery using keyword-based search with BrightData integration. It supports discovering jobs by various search parameters including location, keyword, experience level, job type, and more.

## Features

- ✅ **Zod Validation**: Input validation using Zod schemas
- ✅ **BrightData Integration**: Seamless integration with BrightData discover_by=keyword API
- ✅ **Advanced Search Parameters**: Support for location, keyword, country, time range, job type, experience level, remote work, company, and location radius
- ✅ **Async Operations**: Support for snapshot-based async data discovery
- ✅ **Database Storage**: PostgreSQL storage with search metadata
- ✅ **Comprehensive Swagger**: Detailed API documentation
- ✅ **Search Metadata**: Store search parameters with discovered jobs for better tracking

## API Endpoints

### 1. Discover Job Listings by Keyword
**POST** `/linkedin/job-listing/discover-keyword`

Trigger job discovery using BrightData API with advanced search parameters.

**Request Body:**
```json
{
  "searches": [
    {
      "location": "paris",
      "keyword": "product manager",
      "country": "FR",
      "time_range": "Past month",
      "job_type": "Full-time",
      "experience_level": "Internship",
      "remote": "On-site",
      "company": "",
      "location_radius": ""
    },
    {
      "location": "New York",
      "keyword": "python developer",
      "experience_level": "Executive"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Job discovery started successfully. Use the snapshot_id to check status and retrieve data when ready.",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "search_queries": 2,
  "instructions": {
    "check_status": "GET /linkedin/job-listing/discover-keyword/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/job-listing/discover-keyword/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

### 2. Check Snapshot Status
**GET** `/linkedin/job-listing/discover-keyword/snapshot/{snapshotId}/status`

Check the status of a job discovery snapshot.

**Response:**
```json
{
  "success": true,
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "ready",
  "dataset_id": "gd_lpfll7v5hcqtkxl6l",
  "message": "Snapshot status: ready"
}
```

### 3. Get Snapshot Data
**GET** `/linkedin/job-listing/discover-keyword/snapshot/{snapshotId}/data`

Retrieve and save discovered job listing data from a completed snapshot.

**Response:**
```json
{
  "success": true,
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "ready",
  "message": "Successfully discovered 25 job listings",
  "data": [
    {
      "url": "https://www.linkedin.com/jobs/view/planner-start-people-at-jobster-4270099119?_l=en",
      "job_posting_id": "4270099119",
      "job_title": "Planner - Start People",
      "company_name": "Jobster",
      "job_location": "Doetinchem, Gelderland, Netherlands",
      "job_employment_type": "Full-time",
      "job_num_applicants": 25,
      "search_keyword": "product manager",
      "search_location": "paris"
    }
  ],
  "saved_count": 25
}
```

### 4. Get All Discovered Job Listings
**GET** `/linkedin/job-listing/discover-keyword`

Retrieve all discovered job listings from the database.

### 5. Get Discovered Job Listings by Keyword
**GET** `/linkedin/job-listing/discover-keyword/search/keyword/{keyword}`

Retrieve discovered job listings filtered by search keyword.

### 6. Get Discovered Job Listing by ID
**GET** `/linkedin/job-listing/discover-keyword/{id}`

Retrieve a specific discovered job listing by its database ID.

### 7. Get Discovered Job Listing by Posting ID
**GET** `/linkedin/job-listing/discover-keyword/posting/{postingId}`

Retrieve a specific discovered job listing by its LinkedIn posting ID.

## Search Parameters

The API supports comprehensive search parameters:

- **location** (required): Job location (e.g., "paris", "New York")
- **keyword** (required): Search keyword (e.g., "product manager", "python developer")
- **country** (optional): Country code (e.g., "FR", "US")
- **time_range** (optional): "Past 24 hours", "Past week", "Past month", "Any time"
- **job_type** (optional): "Full-time", "Part-time", "Contract", "Temporary", "Volunteer", "Internship", "Other"
- **experience_level** (optional): "Internship", "Entry level", "Associate", "Mid-Senior level", "Director", "Executive"
- **remote** (optional): "On-site", "Remote", "Hybrid"
- **company** (optional): Company name filter
- **location_radius** (optional): Location radius for search

## Data Structure

The API discovers comprehensive job listing information including:

- **Basic Info**: Job title, company name, location, posting ID
- **Job Details**: Employment type, seniority level, industries, function
- **Application Info**: Number of applicants, application availability, apply link
- **Salary Info**: Base salary, pay range, salary standards
- **Content**: Job summary, formatted description
- **Company Info**: Company URL, logo, company ID
- **Search Metadata**: Original search parameters used to discover the job
- **Metadata**: Posted date/time, discovery input, timestamps

## Search Metadata Storage

Each discovered job listing includes the original search parameters:

- `search_keyword`: The keyword used in the search
- `search_location`: The location used in the search
- `search_country`: The country filter used
- `search_time_range`: The time range filter used
- `search_job_type`: The job type filter used
- `search_experience_level`: The experience level filter used
- `search_remote`: The remote work filter used
- `search_company`: The company filter used
- `search_location_radius`: The location radius used

## Environment Configuration

Required environment variables in `.env`:

```env
# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/linkscrap_db

# BrightData API Configuration
BRIGHTDATA_BASE_URL=https://api.brightdata.com/datasets/v3/trigger
BRIGHTDATA_API_KEY=fc120b5e2ea15b6cf99ba192e3e4b8db6514f0a836b7b4a91dc4f52aa064677a

# Dataset IDs
JOB_LISTING_DISCOVER_DATASET_ID=gd_lpfll7v5hcqtkxl6l

# Application Configuration
NODE_ENV=development
PORT=3000
```

## Setup Instructions

1. **Install Dependencies:**
   ```bash
   npm install
   ```

2. **Start Database:**
   ```bash
   docker-compose up -d postgres
   ```

3. **Build Application:**
   ```bash
   npm run build
   ```

4. **Start Application:**
   ```bash
   npm run start:dev
   ```

5. **Access Swagger Documentation:**
   Open http://localhost:3000/api-docs

## Testing

The application builds successfully and includes:
- ✅ TypeScript compilation
- ✅ Zod validation schemas
- ✅ Swagger documentation
- ✅ Database entity definitions
- ✅ Service layer implementation
- ✅ Controller endpoints
- ✅ BrightData integration

## Architecture

- **Controller**: Handles HTTP requests and responses
- **Service**: Business logic and BrightData integration
- **Entity**: Database schema definition with search metadata
- **DTO**: Input validation with Zod
- **Module**: Dependency injection configuration

## Error Handling

The API includes comprehensive error handling for:
- Invalid search parameters
- BrightData API errors
- Database connection issues
- Missing configuration
- Async operation timeouts

## Logging

Detailed logging throughout the application for:
- Request/response tracking
- BrightData API interactions
- Database operations
- Search metadata tracking
- Error conditions
