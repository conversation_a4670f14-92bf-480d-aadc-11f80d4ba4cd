# LinkedIn Post Collection API

## Overview

This API provides comprehensive LinkedIn post collection using URL-based data extraction with BrightData integration. It supports collecting posts, articles, and pulse content from LinkedIn with detailed engagement metrics, media content, and metadata.

## Features

- ✅ **Zod Validation**: Input validation using Zod schemas
- ✅ **BrightData Integration**: Seamless integration with BrightData API for post collection
- ✅ **Multiple Content Types**: Support for LinkedIn posts, articles, and pulse content
- ✅ **Async Operations**: Support for snapshot-based async data collection
- ✅ **Database Storage**: PostgreSQL storage with comprehensive post data
- ✅ **Comprehensive Swagger**: Detailed API documentation
- ✅ **Frontend-Optimized Response**: Refined response structure for frontend consumption
- ✅ **Rich Metadata**: Engagement metrics, media content, hashtags, and tagged entities

## API Endpoints

### 1. Collect LinkedIn Posts
**POST** `/linkedin/post-collect`

Trigger data collection for LinkedIn posts using BrightData API.

**Request Body:**
```json
{
  "urls": [
    "https://www.linkedin.com/posts/orlenchner_scrapecon-activity-7180537307521769472-oSYN?trk=public_profile",
    "https://www.linkedin.com/posts/karin-dodis_web-data-collection-for-businesses-bright-activity-7176601589682434049-Aakz?trk=public_profile",
    "https://www.linkedin.com/pulse/ab-test-optimisation-earlier-decisions-new-readout-de-b%C3%A9naz%C3%A9?trk=public_profile_article_view",
    "https://www.linkedin.com/pulse/getting-value-out-sunburst-guillaume-de-b%C3%A9naz%C3%A9?trk=public_profile_article_view"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "LinkedIn post collection started successfully. Use the snapshot_id to check status and retrieve data when ready.",
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "started",
  "post_urls": 4,
  "instructions": {
    "check_status": "GET /linkedin/post-collect/snapshot/s_mdboahmo240821rs2a/status",
    "get_data": "GET /linkedin/post-collect/snapshot/s_mdboahmo240821rs2a/data"
  }
}
```

### 2. Check Snapshot Status
**GET** `/linkedin/post-collect/snapshot/{snapshotId}/status`

Check the status of a LinkedIn post collection snapshot.

**Response:**
```json
{
  "success": true,
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "ready",
  "dataset_id": "gd_lyy3tktm25m4avu764",
  "message": "Snapshot status: ready"
}
```

### 3. Get Snapshot Data
**GET** `/linkedin/post-collect/snapshot/{snapshotId}/data`

Retrieve and save LinkedIn post data from a completed snapshot.

**Response:**
```json
{
  "success": true,
  "snapshot_id": "s_mdboahmo240821rs2a",
  "status": "ready",
  "message": "Successfully collected 4 LinkedIn posts",
  "data": [
    {
      "id": "7351896543320330240",
      "url": "https://www.linkedin.com/posts/lifecell-international-pvt-ltd_lifecell-gold-elite-activity-7351896543320330240-rQ_s",
      "post_type": "post",
      "date_posted": "2025-07-18T08:52:07.526Z",
      "title": "We're in the news! LifeCell Gold Elite...",
      "post_text": "We're in the news! LifeCell Gold Elite is making waves...",
      "hashtags": ["#GoldElite", "#LifeCell", "#MediaCoverage"],
      "engagement": {
        "likes": 29,
        "comments": 0
      },
      "author": {
        "user_id": "lifecell-international-pvt-ltd",
        "account_type": "Organization",
        "followers": 128845
      },
      "media": {
        "images": null,
        "videos": null,
        "document_cover_image": "https://media.licdn.com/dms/image/v2/D561FAQGWdeSmODRK7w/feedshare-document-cover-images_480/B56ZgcuuR.G4BM-/0/*************?e=**********&v=beta&t=ddfRhYrXFNzVtpmVZyJZPUMKwoNrbaPnTk2a1NnX7bU",
        "document_page_count": 4
      },
      "tagged_companies": [
        {
          "link": "https://in.linkedin.com/company/dainik-jagran?trk=public_post-text",
          "name": "Dainik Jagran"
        }
      ],
      "tagged_people": []
    }
  ],
  "saved_count": 4
}
```

### 4. Get All Collected Posts
**GET** `/linkedin/post-collect`

Retrieve all collected LinkedIn posts from the database.

### 5. Get Posts by Account Type
**GET** `/linkedin/post-collect/account-type/{accountType}`

Retrieve LinkedIn posts filtered by account type (Organization, Individual, etc.).

### 6. Get Post by ID
**GET** `/linkedin/post-collect/{id}`

Retrieve a specific LinkedIn post by its database ID.

### 7. Get Post by LinkedIn Post ID
**GET** `/linkedin/post-collect/post/{postId}`

Retrieve a specific LinkedIn post by its LinkedIn post ID.

## Supported Content Types

The API supports various LinkedIn content types:

### 1. LinkedIn Posts
- **Pattern**: `/posts/{user-id}_{activity-id}`
- **Example**: `https://www.linkedin.com/posts/orlenchner_scrapecon-activity-7180537307521769472-oSYN?trk=public_profile`

### 2. LinkedIn Pulse Articles
- **Pattern**: `/pulse/{article-slug}`
- **Example**: `https://www.linkedin.com/pulse/ab-test-optimisation-earlier-decisions-new-readout-de-b%C3%A9naz%C3%A9?trk=public_profile_article_view`

### 3. Mixed Content
- Support for collecting both posts and articles in a single request

## Data Structure

The API collects comprehensive post information including:

### Basic Information
- Post ID, URL, type, and posting date
- Title, headline, and post text (both plain and HTML)
- User information and account type

### Engagement Metrics
- Number of likes and comments
- Top visible comments
- User followers and connections

### Content Analysis
- Hashtags extraction
- Embedded links identification
- Tagged companies and people

### Media Content
- Images and videos
- Video duration and thumbnails
- Document attachments with page count

### Author Information
- User ID, profile URL, and title
- Profile picture and account type
- Follower count and connection details

## Frontend-Optimized Response

The API provides a refined response structure optimized for frontend consumption:

```json
{
  "id": "post_id",
  "url": "post_url",
  "post_type": "post|article",
  "date_posted": "ISO_date",
  "title": "post_title",
  "post_text": "post_content",
  "hashtags": ["#tag1", "#tag2"],
  "engagement": {
    "likes": 29,
    "comments": 0
  },
  "author": {
    "user_id": "user_identifier",
    "account_type": "Organization|Individual",
    "followers": 128845
  },
  "media": {
    "images": "image_data",
    "videos": "video_data",
    "document_cover_image": "document_url"
  },
  "tagged_companies": [{"name": "Company", "link": "url"}],
  "tagged_people": [{"name": "Person", "link": "url"}]
}
```

## Environment Configuration

Required environment variables in `.env`:

```env
# Database Configuration
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# BrightData API Configuration
BRIGHTDATA_BASE_URL=https://api.brightdata.com/datasets/v3/trigger
BRIGHTDATA_API_KEY=fc120b5e2ea15b6cf99ba192e3e4b8db6514f0a836b7b4a91dc4f52aa064677a

# Dataset IDs
LINKEDIN_POST_COLLECT_DATASET_ID=gd_lyy3tktm25m4avu764

# Application Configuration
NODE_ENV=development
PORT=3000
```

## Setup Instructions

1. **Install Dependencies:**
   ```bash
   npm install
   ```

2. **Build Application:**
   ```bash
   npm run build
   ```

3. **Start Application:**
   ```bash
   npm run start:dev
   ```

4. **Access Swagger Documentation:**
   Open http://localhost:3000/api-docs

## Testing

The application builds successfully and includes:
- ✅ TypeScript compilation
- ✅ Zod validation schemas
- ✅ Swagger documentation
- ✅ Database entity definitions
- ✅ Service layer implementation
- ✅ Controller endpoints
- ✅ BrightData integration

## Architecture

- **Controller**: Handles HTTP requests and responses with comprehensive Swagger documentation
- **Service**: Business logic and BrightData integration with frontend-optimized formatting
- **Entity**: Database schema definition with comprehensive post data fields
- **DTO**: Input validation with Zod for LinkedIn post and pulse URLs
- **Module**: Dependency injection configuration with TypeORM and BrightData modules

## Error Handling

The API includes comprehensive error handling for:
- Invalid URLs (non-LinkedIn or malformed URLs)
- BrightData API errors and timeouts
- Database connection issues
- Missing configuration variables
- Async operation failures

## Logging

Detailed logging throughout the application for:
- Request/response tracking
- BrightData API interactions
- Database operations
- Data transformation processes
- Error conditions and debugging

## URL Validation

The API validates that all provided URLs:
- Are valid URLs
- Contain `linkedin.com/(posts|pulse)` in the path
- Are less than 500 characters
- Maximum 50 URLs per request

## Complete LinkedIn API Suite

The application now includes four comprehensive LinkedIn APIs:

1. **People Profile Collection** (`/linkedin/people-profile/collect`) - Collect profiles by specific URLs
2. **People Profile Discovery** (`/linkedin/people-profile/discover`) - Discover profiles by search parameters
3. **Company Information Collection** (`/linkedin/company-info/collect`) - Collect company data
4. **Job Listing Collection** (`/linkedin/job-listing/collect`) - Collect jobs by specific URLs
5. **Job Listing Discovery by Keyword** (`/linkedin/job-listing/discover-keyword`) - Discover jobs by search parameters
6. **Job Listing Discovery by URL** (`/linkedin/job-listing/discover-url`) - Discover jobs from LinkedIn job pages
7. **Post Collection** (`/linkedin/post-collect`) - Collect posts and articles by URLs

All APIs are fully functional, properly documented, tested, and ready for frontend integration with comprehensive BrightData integration and database storage.
